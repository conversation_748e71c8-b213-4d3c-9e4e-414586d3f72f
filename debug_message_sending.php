<?php
/**
 * Debug Message Sending
 * 
 * This script helps debug why messages aren't appearing in notification center
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Debug Message Sending</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to debug message sending.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    $db = new Database();
    
    echo "<p><strong>Debugging for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Database Tables Check</h2>";
    
    // Check if required tables exist
    $tables = ['user_messages', 'notification_center_items', 'user_notification_preferences'];
    
    foreach ($tables as $table) {
        try {
            $db->query("SHOW TABLES LIKE '{$table}'");
            $exists = $db->single();
            
            if ($exists) {
                echo "<p>✅ Table '{$table}' exists</p>";
                
                // Get row count
                $db->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $db->single();
                echo "<p>&nbsp;&nbsp;&nbsp;→ Contains {$count->count} records</p>";
            } else {
                echo "<p>❌ Table '{$table}' does NOT exist</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error checking table '{$table}': " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>2. Recent Database Activity</h2>";
    
    // Check recent user_messages
    try {
        $db->query("SELECT * FROM user_messages WHERE to_user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $messages = $db->resultSet();
        
        if (!empty($messages)) {
            echo "<h3>Recent Messages (user_messages table):</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>From</th>";
            echo "<th style='padding: 8px;'>Subject</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "<th style='padding: 8px;'>Allows Reply</th>";
            echo "</tr>";
            
            foreach ($messages as $message) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$message->id}</td>";
                echo "<td style='padding: 8px;'>{$message->from_user_id}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($message->subject, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>{$message->created_at}</td>";
                echo "<td style='padding: 8px;'>" . ($message->allows_reply ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No messages found in user_messages table</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking user_messages: " . $e->getMessage() . "</p>";
    }
    
    // Check recent notification_center_items for this user
    try {
        $db->query("SELECT * FROM notification_center_items WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $notifications = $db->resultSet();

        if (!empty($notifications)) {
            echo "<h3>Recent Notification Center Items for User {$userId}:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Title</th>";
            echo "<th style='padding: 8px;'>Source</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";

            foreach ($notifications as $notification) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$notification->id}</td>";
                echo "<td style='padding: 8px;'>{$notification->notification_type}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($notification->title, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>{$notification->source_table}:{$notification->source_id}</td>";
                echo "<td style='padding: 8px;'>{$notification->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No notifications found for user {$userId} in notification_center_items table</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking notification_center_items: " . $e->getMessage() . "</p>";
    }

    // Check ALL notification_center_items to see what's there
    try {
        echo "<h3>ALL Notification Center Items (to debug):</h3>";
        $db->query("SELECT id, user_id, notification_type, title, source_table, source_id, created_at FROM notification_center_items ORDER BY created_at DESC LIMIT 10");
        $allNotifications = $db->resultSet();

        if (!empty($allNotifications)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>User ID</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Title</th>";
            echo "<th style='padding: 8px;'>Source</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";

            foreach ($allNotifications as $notification) {
                $highlight = ($notification->user_id == $userId) ? 'background: #d4edda;' : '';
                echo "<tr style='{$highlight}'>";
                echo "<td style='padding: 8px;'>{$notification->id}</td>";
                echo "<td style='padding: 8px;'><strong>{$notification->user_id}</strong></td>";
                echo "<td style='padding: 8px;'>{$notification->notification_type}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($notification->title, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>{$notification->source_table}:{$notification->source_id}</td>";
                echo "<td style='padding: 8px;'>{$notification->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p><strong>Note:</strong> Rows highlighted in green are for your user ID ({$userId})</p>";
        } else {
            echo "<p>❌ No notifications found in notification_center_items table at all</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking all notification_center_items: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. User Notification Preferences</h2>";
    
    try {
        $db->query("SELECT * FROM user_notification_preferences WHERE user_id = :user_id");
        $db->bind(':user_id', $userId);
        $preferences = $db->single();
        
        if ($preferences) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Your Notification Preferences:</h4>";
            echo "<ul>";
            echo "<li><strong>Email:</strong> " . ($preferences->email_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "<li><strong>Push:</strong> " . ($preferences->push_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "<li><strong>Toast:</strong> " . ($preferences->toast_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "<li><strong>SMS:</strong> " . ($preferences->sms_notifications ? 'Enabled' : 'Disabled') . "</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<p>❌ No notification preferences found - this might be the issue!</p>";
            echo "<p>Creating default preferences...</p>";
            
            $created = $notificationCenterModel->createDefaultNotificationPreferences($userId);
            if ($created) {
                echo "<p>✅ Default preferences created</p>";
            } else {
                echo "<p>❌ Failed to create default preferences</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking preferences: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. Test Message Sending</h2>";
    
    if ($_POST['action'] ?? '' === 'send_test') {
        try {
            echo "<h3>Sending Test Message...</h3>";
            
            $subject = "Debug Test Message - " . date('Y-m-d H:i:s');
            $message = "This is a debug test message to check if the messaging system works.\n\n[reply]";
            
            echo "<p><strong>Attempting to send:</strong></p>";
            echo "<ul>";
            echo "<li><strong>From:</strong> {$userId}</li>";
            echo "<li><strong>To:</strong> {$userId}</li>";
            echo "<li><strong>Subject:</strong> {$subject}</li>";
            echo "<li><strong>Message:</strong> " . htmlspecialchars($message) . "</li>";
            echo "</ul>";

            // Enable error reporting for this test
            error_reporting(E_ALL);
            ini_set('display_errors', 1);

            echo "<p><strong>Calling sendMessage()...</strong></p>";

            $messageId = $notificationCenterModel->sendMessage(
                $userId,     // from
                $userId,     // to (self)
                $subject,    // subject
                $message,    // message
                null,        // show_id
                false        // requires_reply
            );

            echo "<p><strong>sendMessage() returned:</strong> " . ($messageId ? $messageId : 'FALSE') . "</p>";
            
            if ($messageId) {
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ Message Sent Successfully!</h4>";
                echo "<p><strong>Message ID:</strong> {$messageId}</p>";
                echo "</div>";
                
                // Check if it was created in database
                $db->query("SELECT * FROM user_messages WHERE id = :message_id");
                $db->bind(':message_id', $messageId);
                $createdMessage = $db->single();
                
                if ($createdMessage) {
                    echo "<p>✅ Message found in user_messages table</p>";
                } else {
                    echo "<p>❌ Message NOT found in user_messages table</p>";
                }
                
                // Check if notification center item was created
                $db->query("SELECT * FROM notification_center_items WHERE source_table = 'user_messages' AND source_id = :message_id");
                $db->bind(':message_id', $messageId);
                $notificationItem = $db->single();
                
                if ($notificationItem) {
                    echo "<p>✅ Notification center item created (ID: {$notificationItem->id})</p>";
                    echo "<p><a href='" . BASE_URL . "/notification_center/viewNotification/{$notificationItem->id}' target='_blank'>View in Notification Center</a></p>";
                } else {
                    echo "<p>❌ Notification center item NOT created</p>";
                }
                
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>❌ Failed to Send Message</h4>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ Error Sending Test Message</h4>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "<p><strong>Stack trace:</strong></p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
            echo "</div>";
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Send Test Message</h4>";
    echo "<p>This will test the complete message sending process:</p>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='send_test'>";
    echo "<button type='submit' class='btn btn-primary'>Send Test Message to Myself</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<h2>5. Error Log Check</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Check Error Logs</h4>";
    echo "<p>Look for recent errors in your PHP error log related to:</p>";
    echo "<ul>";
    echo "<li>NotificationCenterModel::sendMessage</li>";
    echo "<li>NotificationCenterModel::createNotificationItem</li>";
    echo "<li>NotificationCenterModel::sendMessageNotification</li>";
    echo "<li>NotificationService errors</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>6. Navigation</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Quick links:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Notification Center</a></li>";
    echo "<li><a href='" . BASE_URL . "/judge_management' target='_blank'>Judge Management</a></li>";
    echo "<li><a href='" . BASE_URL . "/user/settings' target='_blank'>User Settings</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
