<?php
/**
 * Test Judge Messaging System
 * 
 * This script tests the exact same method that Judge Management uses
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Test Judge Messaging System</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to test judge messaging.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    
    echo "<p><strong>Testing for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Test Judge Management sendMessage() Call</h2>";
    
    if ($_POST['action'] ?? '' === 'test_judge_message') {
        try {
            echo "<h3>Simulating Judge Management Message...</h3>";
            
            // These are the exact parameters that Judge Management uses
            $senderId = $userId;  // Current user (coordinator/admin)
            $judgeId = $userId;   // Send to self for testing
            $showId = 1;          // Dummy show ID
            $subject = "Test Judge Message - " . date('Y-m-d H:i:s');
            $message = "This is a test message from Judge Management system.\n\nPlease confirm you received this message.";
            
            echo "<p><strong>Parameters:</strong></p>";
            echo "<ul>";
            echo "<li><strong>From User ID:</strong> {$senderId}</li>";
            echo "<li><strong>To User ID:</strong> {$judgeId}</li>";
            echo "<li><strong>Show ID:</strong> {$showId}</li>";
            echo "<li><strong>Subject:</strong> " . htmlspecialchars($subject) . "</li>";
            echo "<li><strong>Message:</strong> " . htmlspecialchars($message) . "</li>";
            echo "</ul>";
            
            echo "<p><strong>Calling NotificationCenterModel::sendMessage()...</strong></p>";
            
            // This is the EXACT call that Judge Management makes
            $result = $notificationCenterModel->sendMessage(
                $senderId,
                $judgeId,
                $subject,
                $message . "\n\n[reply]", // Add [reply] to allow responses (like Judge Management does)
                $showId,
                false // requires_reply flag
            );
            
            if ($result) {
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ Judge Message Sent Successfully!</h4>";
                echo "<p><strong>Result ID:</strong> {$result}</p>";
                echo "<p>This proves the Judge Management messaging system should work.</p>";
                echo "</div>";
                
                // Check what was created
                echo "<h4>Checking Results:</h4>";
                
                // Check notification center
                $notifications = $notificationCenterModel->getUserNotifications($judgeId, 'message', 'all', 5, 0);
                if (!empty($notifications)) {
                    echo "<p>✅ Found " . count($notifications) . " message notifications in notification center</p>";
                    
                    $latestNotification = $notifications[0];
                    echo "<ul>";
                    echo "<li><strong>ID:</strong> {$latestNotification->id}</li>";
                    echo "<li><strong>Title:</strong> " . htmlspecialchars($latestNotification->title) . "</li>";
                    echo "<li><strong>Type:</strong> {$latestNotification->notification_type}</li>";
                    echo "<li><strong>Created:</strong> {$latestNotification->created_at}</li>";
                    echo "</ul>";
                    
                    $viewUrl = BASE_URL . "/notification_center";
                    echo "<p><a href='{$viewUrl}' target='_blank' class='btn btn-primary'>View in Notification Center</a></p>";
                } else {
                    echo "<p>❌ No message notifications found in notification center</p>";
                }
                
                // Check notification queue
                require_once APPROOT . '/models/NotificationModel.php';
                $notificationModel = new NotificationModel();
                $queueItems = $notificationModel->getQueueItems('pending', 10, 0);
                
                $recentItems = array_filter($queueItems, function($item) use ($judgeId) {
                    return $item->user_id == $judgeId && strtotime($item->created_at) > (time() - 300); // Last 5 minutes
                });
                
                if (!empty($recentItems)) {
                    echo "<p>✅ Found " . count($recentItems) . " recent queue items for user {$judgeId}</p>";
                    foreach ($recentItems as $item) {
                        echo "<ul>";
                        echo "<li><strong>Type:</strong> {$item->notification_type}</li>";
                        echo "<li><strong>Status:</strong> {$item->status}</li>";
                        echo "<li><strong>Subject:</strong> " . htmlspecialchars($item->subject) . "</li>";
                        echo "</ul>";
                    }
                } else {
                    echo "<p>❌ No recent queue items found for user {$judgeId}</p>";
                }
                
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>❌ Judge Message Failed</h4>";
                echo "<p>The sendMessage() call returned false or null.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ Error in Judge Message Test</h4>";
            echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>Stack trace:</strong></p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
            echo "</div>";
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Test Judge Management Messaging</h4>";
    echo "<p>This will test the exact same method and parameters that Judge Management uses:</p>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='test_judge_message'>";
    echo "<button type='submit' class='btn btn-primary'>Test Judge Management sendMessage()</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<h2>2. Check Current Notification Center</h2>";
    
    // Show current notification center state
    try {
        $notifications = $notificationCenterModel->getUserNotifications($userId, 'all', 'all', 10, 0);
        
        if (!empty($notifications)) {
            echo "<h3>Your Current Notifications:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Title</th>";
            echo "<th style='padding: 8px;'>Read</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";
            
            foreach ($notifications as $notification) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$notification->id}</td>";
                echo "<td style='padding: 8px;'>{$notification->notification_type}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($notification->title, 0, 40)) . "...</td>";
                echo "<td style='padding: 8px;'>" . ($notification->is_read ? 'Yes' : 'No') . "</td>";
                echo "<td style='padding: 8px;'>{$notification->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No notifications found in your notification center</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking notification center: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Navigation</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Related tools:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Notification Center</a></li>";
    echo "<li><a href='" . BASE_URL . "/judge_management' target='_blank'>Judge Management</a></li>";
    echo "<li><a href='" . BASE_URL . "/debug_message_sending.php' target='_blank'>Debug Message Sending</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
