<?php
/**
 * Notification Center Model
 * 
 * This model handles all notification center functionality including
 * unified notification display, message management, and read/unread tracking.
 */
class NotificationCenterModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all notifications for a user with filtering
     */
    public function getUserNotifications($userId, $type = 'all', $status = 'all', $limit = 20, $offset = 0) {
        $sql = "SELECT 
                    nci.*,
                    u.name as from_user_name,
                    u.email as from_user_email
                FROM notification_center_items nci
                LEFT JOIN user_messages um ON (nci.source_table = 'user_messages' AND nci.source_id = um.id)
                LEFT JOIN users u ON um.from_user_id = u.id
                WHERE nci.user_id = :user_id";
        
        $params = [':user_id' => $userId];
        
        // Add type filter
        if ($type !== 'all') {
            $sql .= " AND nci.notification_type = :type";
            $params[':type'] = $type;
        }
        
        // Add status filter
        if ($status === 'unread') {
            $sql .= " AND nci.is_read = 0";
        } elseif ($status === 'read') {
            $sql .= " AND nci.is_read = 1";
        }
        
        // Add archived filter (exclude archived by default)
        if ($status !== 'archived') {
            $sql .= " AND nci.is_archived = 0";
        } else {
            $sql .= " AND nci.is_archived = 1";
        }
        
        $sql .= " ORDER BY nci.created_at DESC LIMIT :limit OFFSET :offset";
        
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind($key, $value);
        }
        $this->db->bind(':limit', $limit, PDO::PARAM_INT);
        $this->db->bind(':offset', $offset, PDO::PARAM_INT);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get notification counts by type and status
     */
    public function getNotificationCounts($userId) {
        $sql = "SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type";

        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $results = $this->db->resultSet();

        $counts = [
            'total_unread' => 0,
            'total_count' => 0,
            'archived_count' => 0,
            'message' => ['unread' => 0, 'total' => 0, 'archived' => 0],
            'push' => ['unread' => 0, 'total' => 0, 'archived' => 0],
            'toast' => ['unread' => 0, 'total' => 0, 'archived' => 0],
            'system' => ['unread' => 0, 'total' => 0, 'archived' => 0],
            'event' => ['unread' => 0, 'total' => 0, 'archived' => 0],
            'judging' => ['unread' => 0, 'total' => 0, 'archived' => 0]
        ];

        foreach ($results as $result) {
            $type = $result->notification_type;
            $counts[$type]['unread'] = (int)$result->unread_count;
            $counts[$type]['total'] = (int)$result->total_count;
            $counts[$type]['archived'] = (int)$result->archived_count;
            $counts['total_unread'] += (int)$result->unread_count;
            $counts['total_count'] += (int)$result->total_count;
            $counts['archived_count'] += (int)$result->archived_count;
        }

        return $counts;
    }
    
    /**
     * Get total notification count for pagination
     */
    public function getTotalNotificationCount($userId, $type = 'all', $status = 'all') {
        $sql = "SELECT COUNT(*) as count FROM notification_center_items WHERE user_id = :user_id";
        $params = [':user_id' => $userId];
        
        if ($type !== 'all') {
            $sql .= " AND notification_type = :type";
            $params[':type'] = $type;
        }
        
        if ($status === 'unread') {
            $sql .= " AND is_read = 0";
        } elseif ($status === 'read') {
            $sql .= " AND is_read = 1";
        }
        
        if ($status !== 'archived') {
            $sql .= " AND is_archived = 0";
        } else {
            $sql .= " AND is_archived = 1";
        }
        
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind($key, $value);
        }
        
        $result = $this->db->single();
        return (int)$result->count;
    }
    
    /**
     * Get a specific notification by ID
     */
    public function getNotificationById($notificationId, $userId) {
        $sql = "SELECT 
                    nci.*,
                    u.name as from_user_name,
                    u.email as from_user_email
                FROM notification_center_items nci
                LEFT JOIN user_messages um ON (nci.source_table = 'user_messages' AND nci.source_id = um.id)
                LEFT JOIN users u ON um.from_user_id = u.id
                WHERE nci.id = :notification_id AND nci.user_id = :user_id";
        
        $this->db->query($sql);
        $this->db->bind(':notification_id', $notificationId);
        $this->db->bind(':user_id', $userId);
        
        return $this->db->single();
    }
    
    /**
     * Mark a notification as read
     */
    public function markAsRead($notificationId, $userId) {
        $this->db->query("UPDATE notification_center_items 
                         SET is_read = 1, read_at = NOW() 
                         WHERE id = :notification_id AND user_id = :user_id");
        $this->db->bind(':notification_id', $notificationId);
        $this->db->bind(':user_id', $userId);
        
        $success = $this->db->execute();
        
        // Also mark the source notification as read if it exists
        if ($success) {
            $this->markSourceNotificationAsRead($notificationId);
        }
        
        return $success;
    }
    
    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsRead($userId, $type = 'all') {
        $sql = "UPDATE notification_center_items 
                SET is_read = 1, read_at = NOW() 
                WHERE user_id = :user_id AND is_read = 0";
        
        $params = [':user_id' => $userId];
        
        if ($type !== 'all') {
            $sql .= " AND notification_type = :type";
            $params[':type'] = $type;
        }
        
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind($key, $value);
        }
        
        return $this->db->execute();
    }
    
    /**
     * Archive a notification
     */
    public function archiveNotification($notificationId, $userId) {
        $this->db->query("UPDATE notification_center_items
                         SET is_archived = 1
                         WHERE id = :notification_id AND user_id = :user_id");
        $this->db->bind(':notification_id', $notificationId);
        $this->db->bind(':user_id', $userId);

        return $this->db->execute();
    }

    /**
     * Unarchive a notification
     */
    public function unarchiveNotification($notificationId, $userId) {
        $this->db->query("UPDATE notification_center_items
                         SET is_archived = 0
                         WHERE id = :notification_id AND user_id = :user_id");
        $this->db->bind(':notification_id', $notificationId);
        $this->db->bind(':user_id', $userId);

        return $this->db->execute();
    }

    /**
     * Delete a notification permanently
     */
    public function deleteNotification($notificationId, $userId) {
        try {
            $this->db->beginTransaction();

            // Get the notification details first
            $notification = $this->getNotificationById($notificationId, $userId);

            if (!$notification) {
                throw new Exception("Notification not found");
            }

            // Delete from notification center
            $this->db->query("DELETE FROM notification_center_items
                             WHERE id = :notification_id AND user_id = :user_id");
            $this->db->bind(':notification_id', $notificationId);
            $this->db->bind(':user_id', $userId);

            if (!$this->db->execute()) {
                throw new Exception("Failed to delete notification center item");
            }

            // Also delete from source table if it exists
            if ($notification->source_table && $notification->source_id) {
                $this->deleteSourceNotification($notification->source_table, $notification->source_id, $userId);
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("NotificationCenterModel::deleteNotification - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete source notification (helper method)
     */
    private function deleteSourceNotification($sourceTable, $sourceId, $userId) {
        $allowedTables = ['user_messages', 'user_push_notifications', 'user_toast_notifications'];

        if (!in_array($sourceTable, $allowedTables)) {
            return; // Skip deletion for unknown tables
        }

        try {
            if ($sourceTable === 'user_messages') {
                $this->db->query("DELETE FROM user_messages
                                 WHERE id = :source_id AND (from_user_id = :user_id OR to_user_id = :user_id)");
            } else {
                $this->db->query("DELETE FROM {$sourceTable}
                                 WHERE id = :source_id AND user_id = :user_id");
            }

            $this->db->bind(':source_id', $sourceId);
            $this->db->bind(':user_id', $userId);
            $this->db->execute();

        } catch (Exception $e) {
            error_log("NotificationCenterModel::deleteSourceNotification - Error: " . $e->getMessage());
            // Don't throw - this is a cleanup operation
        }
    }
    
    /**
     * Create a notification center item
     */
    public function createNotificationItem($userId, $type, $title, $message, $sourceTable = null, $sourceId = null, $actionUrl = null, $actionText = null, $metadata = null) {
        $sql = "INSERT INTO notification_center_items 
                (user_id, notification_type, source_table, source_id, title, message, action_url, action_text, metadata, created_at) 
                VALUES (:user_id, :type, :source_table, :source_id, :title, :message, :action_url, :action_text, :metadata, NOW())";
        
        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':type', $type);
        $this->db->bind(':source_table', $sourceTable);
        $this->db->bind(':source_id', $sourceId);
        $this->db->bind(':title', $title);
        $this->db->bind(':message', $message);
        $this->db->bind(':action_url', $actionUrl);
        $this->db->bind(':action_text', $actionText);
        $this->db->bind(':metadata', $metadata ? json_encode($metadata) : null);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Send a message and create notification center item
     */
    public function sendMessage($fromUserId, $toUserId, $subject, $message, $showId = null, $requiresReply = false) {
        try {
            $this->db->beginTransaction();

            // Check if message contains [reply] to allow replies
            $allowsReply = strpos($message, '[reply]') !== false;

            // Insert into user_messages
            $sql = "INSERT INTO user_messages
                    (from_user_id, to_user_id, subject, message, show_id, requires_reply, allows_reply, reply_used, created_at)
                    VALUES (:from_user_id, :to_user_id, :subject, :message, :show_id, :requires_reply, :allows_reply, 0, NOW())";

            $this->db->query($sql);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->bind(':to_user_id', $toUserId);
            $this->db->bind(':subject', $subject);
            $this->db->bind(':message', $message);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':requires_reply', $requiresReply ? 1 : 0);
            $this->db->bind(':allows_reply', $allowsReply ? 1 : 0);

            if (!$this->db->execute()) {
                throw new Exception("Failed to insert message");
            }

            $messageId = $this->db->lastInsertId();
            
            // Create notification center item
            $actionUrl = "/notification_center/viewNotification/{$messageId}";
            $actionText = $requiresReply ? "Reply" : "View";
            
            $notificationId = $this->createNotificationItem(
                $toUserId,
                'message',
                $subject,
                $message,
                'user_messages',
                $messageId,
                $actionUrl,
                $actionText,
                ['show_id' => $showId, 'requires_reply' => $requiresReply]
            );
            
            if (!$notificationId) {
                throw new Exception("Failed to create notification center item");
            }

            // Send notification using recipient's preferences
            $this->sendMessageNotification($fromUserId, $toUserId, $subject, $message, $showId);

            $this->db->commit();
            return $messageId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("NotificationCenterModel::sendMessage - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send a reply to a message
     */
    public function sendReply($fromUserId, $toUserId, $originalSubject, $message, $parentMessageId, $showId = null) {
        $subject = strpos($originalSubject, 'Re: ') === 0 ? $originalSubject : 'Re: ' . $originalSubject;

        try {
            $this->db->beginTransaction();

            // Insert reply message
            $sql = "INSERT INTO user_messages
                    (from_user_id, to_user_id, subject, message, show_id, parent_message_id, created_at)
                    VALUES (:from_user_id, :to_user_id, :subject, :message, :show_id, :parent_message_id, NOW())";

            $this->db->query($sql);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->bind(':to_user_id', $toUserId);
            $this->db->bind(':subject', $subject);
            $this->db->bind(':message', $message);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':parent_message_id', $parentMessageId);

            if (!$this->db->execute()) {
                throw new Exception("Failed to insert reply");
            }

            $replyId = $this->db->lastInsertId();

            // Create notification center item for the reply recipient
            $actionUrl = "/notification_center/viewNotification/{$replyId}";

            $notificationId = $this->createNotificationItem(
                $toUserId,
                'message',
                $subject,
                $message,
                'user_messages',
                $replyId,
                $actionUrl,
                'Reply',
                ['show_id' => $showId, 'parent_message_id' => $parentMessageId, 'requires_reply' => true]
            );

            if (!$notificationId) {
                throw new Exception("Failed to create notification center item for reply");
            }

            // Mark the original message's reply as used (for non-privileged users)
            require_once APPROOT . '/core/Auth.php';
            $auth = new Auth();

            // Only mark reply as used for regular users (not admin/coordinator/judge/staff)
            if (!$auth->hasRole(['admin', 'coordinator', 'judge', 'staff'])) {
                $this->markReplyAsUsed($parentMessageId, $fromUserId);
            }

            // Send notification using recipient's preferences (not exposing emails)
            $this->sendReplyNotification($fromUserId, $toUserId, $subject, $message, $showId);

            $this->db->commit();
            return $replyId;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("NotificationCenterModel::sendReply - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send reply notification using recipient's preferences
     */
    private function sendReplyNotification($fromUserId, $toUserId, $subject, $message, $showId = null) {
        try {
            // Get sender's name (not email)
            $this->db->query("SELECT name FROM users WHERE id = :user_id");
            $this->db->bind(':user_id', $fromUserId);
            $sender = $this->db->single();
            $senderName = $sender ? $sender->name : 'Someone';

            // Create notification message without exposing email
            $notificationSubject = "Reply: {$subject}";
            $notificationMessage = "You received a reply from {$senderName}:\n\n{$message}";

            // Send notifications based on user preferences
            $this->sendNotificationViaUserPreferences($toUserId, $notificationSubject, $notificationMessage);

        } catch (Exception $e) {
            error_log("NotificationCenterModel::sendReplyNotification - Error: " . $e->getMessage());
            // Don't throw - this is a notification failure, not a reply failure
        }
    }

    /**
     * Send message notification using recipient's preferences
     */
    private function sendMessageNotification($fromUserId, $toUserId, $subject, $message, $showId = null) {
        try {
            // Get sender's name (not email)
            $this->db->query("SELECT name FROM users WHERE id = :user_id");
            $this->db->bind(':user_id', $fromUserId);
            $sender = $this->db->single();
            $senderName = $sender ? $sender->name : 'Someone';

            // Create notification message without exposing email
            $notificationSubject = $subject;
            $notificationMessage = "You received a message from {$senderName}:\n\n{$message}";

            // Send notifications based on user preferences
            $this->sendNotificationViaUserPreferences($toUserId, $notificationSubject, $notificationMessage);

        } catch (Exception $e) {
            error_log("NotificationCenterModel::sendMessageNotification - Error: " . $e->getMessage());
            // Don't throw - this is a notification failure, not a message failure
        }
    }

    /**
     * Send notification via user's enabled preferences
     */
    private function sendNotificationViaUserPreferences($userId, $subject, $message) {
        try {
            // Get user preferences
            $this->db->query("SELECT
                                email_notifications,
                                push_notifications,
                                toast_notifications,
                                sms_notifications
                             FROM user_notification_preferences
                             WHERE user_id = :user_id");
            $this->db->bind(':user_id', $userId);
            $preferences = $this->db->single();

            // If no preferences exist, create default ones
            if (!$preferences) {
                $this->createDefaultNotificationPreferences($userId);
                // Set default preferences (email and push enabled)
                $preferences = (object)[
                    'email_notifications' => 1,
                    'push_notifications' => 1,
                    'toast_notifications' => 1,
                    'sms_notifications' => 0
                ];
            }

            // Load notification service
            require_once APPROOT . '/models/NotificationService.php';
            $notificationService = new NotificationService();

            $sentCount = 0;

            // Send email if enabled
            if ($preferences->email_notifications) {
                try {
                    $sent = $notificationService->sendTestNotification($userId, 'email', $subject, $message);
                    if ($sent) $sentCount++;
                } catch (Exception $e) {
                    error_log("NotificationCenterModel::sendNotificationViaUserPreferences - Email error: " . $e->getMessage());
                }
            }

            // Send push if enabled
            if ($preferences->push_notifications) {
                try {
                    $sent = $notificationService->sendTestNotification($userId, 'push', $subject, $message);
                    if ($sent) $sentCount++;
                } catch (Exception $e) {
                    error_log("NotificationCenterModel::sendNotificationViaUserPreferences - Push error: " . $e->getMessage());
                }
            }

            // Send toast if enabled
            if ($preferences->toast_notifications) {
                try {
                    $sent = $notificationService->sendTestNotification($userId, 'toast', $subject, $message);
                    if ($sent) $sentCount++;
                } catch (Exception $e) {
                    error_log("NotificationCenterModel::sendNotificationViaUserPreferences - Toast error: " . $e->getMessage());
                }
            }

            // Send SMS if enabled
            if ($preferences->sms_notifications) {
                try {
                    $sent = $notificationService->sendTestNotification($userId, 'sms', $subject, $message);
                    if ($sent) $sentCount++;
                } catch (Exception $e) {
                    error_log("NotificationCenterModel::sendNotificationViaUserPreferences - SMS error: " . $e->getMessage());
                }
            }

            if (DEBUG_MODE) {
                error_log("NotificationCenterModel::sendNotificationViaUserPreferences - Sent $sentCount notifications to user $userId");
            }

        } catch (Exception $e) {
            error_log("NotificationCenterModel::sendNotificationViaUserPreferences - Error: " . $e->getMessage());
        }
    }

    /**
     * Get message by ID
     */
    public function getMessageById($messageId) {
        $this->db->query("SELECT * FROM user_messages WHERE id = :message_id");
        $this->db->bind(':message_id', $messageId);
        return $this->db->single();
    }

    /**
     * Get message thread (conversation)
     */
    public function getMessageThread($messageId) {
        // Get the root message
        $this->db->query("SELECT * FROM user_messages WHERE id = :message_id");
        $this->db->bind(':message_id', $messageId);
        $rootMessage = $this->db->single();

        if (!$rootMessage) {
            return [];
        }

        // Find the actual root (in case this is already a reply)
        $rootId = $rootMessage->parent_message_id ?: $messageId;

        // Get all messages in the thread
        $sql = "SELECT
                    um.*,
                    u.name as from_user_name,
                    u.email as from_user_email
                FROM user_messages um
                JOIN users u ON um.from_user_id = u.id
                WHERE um.id = :root_id OR um.parent_message_id = :root_id
                ORDER BY um.created_at ASC";

        $this->db->query($sql);
        $this->db->bind(':root_id', $rootId);

        return $this->db->resultSet();
    }

    /**
     * Mark source notification as read (for push/toast notifications)
     */
    private function markSourceNotificationAsRead($notificationCenterId) {
        // Get the notification center item
        $this->db->query("SELECT source_table, source_id FROM notification_center_items WHERE id = :id");
        $this->db->bind(':id', $notificationCenterId);
        $item = $this->db->single();

        if (!$item || !$item->source_table || !$item->source_id) {
            return;
        }

        // Mark the source as read
        if ($item->source_table === 'user_push_notifications') {
            $this->db->query("UPDATE user_push_notifications SET is_read = 1 WHERE id = :id");
            $this->db->bind(':id', $item->source_id);
            $this->db->execute();
        } elseif ($item->source_table === 'user_toast_notifications') {
            $this->db->query("UPDATE user_toast_notifications SET is_read = 1 WHERE id = :id");
            $this->db->bind(':id', $item->source_id);
            $this->db->execute();
        } elseif ($item->source_table === 'user_messages') {
            $this->db->query("UPDATE user_messages SET is_read = 1, read_at = NOW() WHERE id = :id");
            $this->db->bind(':id', $item->source_id);
            $this->db->execute();
        }
    }

    /**
     * Sync existing notifications to notification center
     * This method helps migrate existing push/toast notifications
     */
    public function syncExistingNotifications($userId) {
        try {
            $this->db->beginTransaction();

            // Sync push notifications
            $sql = "INSERT INTO notification_center_items
                    (user_id, notification_type, source_table, source_id, title, message, is_read, created_at)
                    SELECT
                        user_id,
                        'push' as notification_type,
                        'user_push_notifications' as source_table,
                        id as source_id,
                        title,
                        message,
                        is_read,
                        created_at
                    FROM user_push_notifications
                    WHERE user_id = :user_id
                    AND id NOT IN (
                        SELECT source_id FROM notification_center_items
                        WHERE source_table = 'user_push_notifications' AND user_id = :user_id
                    )";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
            $this->db->execute();

            // Sync toast notifications
            $sql = "INSERT INTO notification_center_items
                    (user_id, notification_type, source_table, source_id, title, message, is_read, created_at)
                    SELECT
                        user_id,
                        'toast' as notification_type,
                        'user_toast_notifications' as source_table,
                        id as source_id,
                        title,
                        message,
                        is_read,
                        created_at
                    FROM user_toast_notifications
                    WHERE user_id = :user_id
                    AND id NOT IN (
                        SELECT source_id FROM notification_center_items
                        WHERE source_table = 'user_toast_notifications' AND user_id = :user_id
                    )";

            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
            $this->db->execute();

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("NotificationCenterModel::syncExistingNotifications - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up old archived notifications
     * Deletes archived notifications from completed shows older than specified days
     */
    public function cleanupOldArchivedNotifications($daysAfterShowEnd = 14) {
        try {
            $this->db->beginTransaction();

            // Get notifications related to shows that ended more than X days ago
            $sql = "SELECT nci.id, nci.source_table, nci.source_id
                    FROM notification_center_items nci
                    LEFT JOIN user_messages um ON (nci.source_table = 'user_messages' AND nci.source_id = um.id)
                    LEFT JOIN shows s ON um.show_id = s.id
                    WHERE nci.is_archived = 1
                    AND s.end_date IS NOT NULL
                    AND s.end_date < DATE_SUB(NOW(), INTERVAL :days DAY)";

            $this->db->query($sql);
            $this->db->bind(':days', $daysAfterShowEnd);
            $oldNotifications = $this->db->resultSet();

            $deletedCount = 0;

            foreach ($oldNotifications as $notification) {
                // Delete the notification center item
                $this->db->query("DELETE FROM notification_center_items WHERE id = :id");
                $this->db->bind(':id', $notification->id);

                if ($this->db->execute()) {
                    $deletedCount++;

                    // Also delete source if it exists
                    if ($notification->source_table && $notification->source_id) {
                        $this->deleteSourceNotificationById($notification->source_table, $notification->source_id);
                    }
                }
            }

            // Also clean up archived notifications older than 90 days regardless of show status
            $sql = "DELETE FROM notification_center_items
                    WHERE is_archived = 1
                    AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)";

            $this->db->query($sql);
            $this->db->execute();
            $generalCleanupCount = $this->db->rowCount();

            $this->db->commit();

            return [
                'show_related_deleted' => $deletedCount,
                'general_cleanup_deleted' => $generalCleanupCount,
                'total_deleted' => $deletedCount + $generalCleanupCount
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("NotificationCenterModel::cleanupOldArchivedNotifications - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete source notification by ID (helper for cleanup)
     */
    private function deleteSourceNotificationById($sourceTable, $sourceId) {
        $allowedTables = ['user_messages', 'user_push_notifications', 'user_toast_notifications'];

        if (!in_array($sourceTable, $allowedTables)) {
            return;
        }

        try {
            $this->db->query("DELETE FROM {$sourceTable} WHERE id = :source_id");
            $this->db->bind(':source_id', $sourceId);
            $this->db->execute();

        } catch (Exception $e) {
            error_log("NotificationCenterModel::deleteSourceNotificationById - Error: " . $e->getMessage());
        }
    }

    /**
     * Check if user can send notifications (has at least one notification type enabled)
     */
    public function canUserSendNotifications($userId) {
        try {
            $this->db->query("SELECT
                                email_notifications,
                                push_notifications,
                                toast_notifications,
                                sms_notifications
                             FROM user_notification_preferences
                             WHERE user_id = :user_id");
            $this->db->bind(':user_id', $userId);
            $preferences = $this->db->single();

            // If no preferences exist, create default ones and allow sending
            if (!$preferences) {
                $this->createDefaultNotificationPreferences($userId);
                return true; // Default preferences allow sending
            }

            // Check if at least one notification type is enabled
            return ($preferences->email_notifications == 1 ||
                    $preferences->push_notifications == 1 ||
                    $preferences->toast_notifications == 1 ||
                    $preferences->sms_notifications == 1);

        } catch (Exception $e) {
            error_log("NotificationCenterModel::canUserSendNotifications - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's notification preferences
     */
    public function getUserNotificationPreferences($userId) {
        try {
            $this->db->query("SELECT
                                u.name,
                                unp.email_notifications,
                                unp.push_notifications,
                                unp.toast_notifications,
                                unp.sms_notifications
                             FROM users u
                             LEFT JOIN user_notification_preferences unp ON u.id = unp.user_id
                             WHERE u.id = :user_id");
            $this->db->bind(':user_id', $userId);
            $result = $this->db->single();

            // If no preferences exist, create default ones
            if ($result && !$result->email_notifications && !$result->push_notifications && !$result->toast_notifications && !$result->sms_notifications) {
                $this->createDefaultNotificationPreferences($userId);
                return $this->getUserNotificationPreferences($userId);
            }

            return $result;

        } catch (Exception $e) {
            error_log("NotificationCenterModel::getUserNotificationPreferences - Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create default notification preferences for a user
     */
    private function createDefaultNotificationPreferences($userId) {
        try {
            $this->db->query("INSERT INTO user_notification_preferences
                             (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications,
                              event_reminders, registration_updates, judging_updates, award_notifications,
                              system_announcements, reminder_times)
                             VALUES (:user_id, 1, 0, 1, 1, 1, 1, 1, 1, 1, :reminder_times)
                             ON DUPLICATE KEY UPDATE
                             email_notifications = VALUES(email_notifications),
                             push_notifications = VALUES(push_notifications),
                             toast_notifications = VALUES(toast_notifications)");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':reminder_times', '[1440, 60]');

            return $this->db->execute();

        } catch (Exception $e) {
            error_log("NotificationCenterModel::createDefaultNotificationPreferences - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user can reply to a specific message
     */
    public function canUserReplyToMessage($userId, $messageId) {
        try {
            // Get the message details
            $this->db->query("SELECT
                                from_user_id,
                                to_user_id,
                                allows_reply,
                                reply_used,
                                show_id
                             FROM user_messages
                             WHERE id = :message_id");
            $this->db->bind(':message_id', $messageId);
            $message = $this->db->single();

            if (!$message) {
                return false;
            }

            // Check if user is the recipient of this message
            if ($message->to_user_id != $userId) {
                return false;
            }

            // Check if user has privileged role using existing Auth system
            require_once APPROOT . '/core/Auth.php';
            $auth = new Auth();

            // Admin, coordinator, judge, staff can reply to any message
            if ($auth->hasRole(['admin', 'coordinator', 'judge', 'staff'])) {
                return true;
            }

            // For regular users: check if message allows reply and hasn't been used
            return ($message->allows_reply == 1 && $message->reply_used == 0);

        } catch (Exception $e) {
            error_log("NotificationCenterModel::canUserReplyToMessage - Error: " . $e->getMessage());
            return false;
        }
    }



    /**
     * Mark reply as used for a message
     */
    public function markReplyAsUsed($messageId, $userId) {
        try {
            $this->db->query("UPDATE user_messages
                             SET reply_used = 1
                             WHERE id = :message_id
                             AND to_user_id = :user_id");
            $this->db->bind(':message_id', $messageId);
            $this->db->bind(':user_id', $userId);

            return $this->db->execute();

        } catch (Exception $e) {
            error_log("NotificationCenterModel::markReplyAsUsed - Error: " . $e->getMessage());
            return false;
        }
    }
}
