[12-Jul-2025 18:55:54 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:55:54 UTC] Raw URL: notification_center/getUnreadCount
[12-Jul-2025 18:55:54 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:55:54 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:55:54 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:55:54 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:55:54 UTC] Session check - Current time: 1752346554
[12-Jul-2025 18:55:54 UTC] Session check - Login time: 1752346554
[12-Jul-2025 18:55:54 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:55:54 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:55:54 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:55:54 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:55:54 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:55:54 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:55:54 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:55:54 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[12-Jul-2025 18:55:54 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[12-Jul-2025 18:55:54 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[12-Jul-2025 18:55:54 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[12-Jul-2025 18:55:54 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:55:54 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:55:54 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"34","archived_count":"0"}
[12-Jul-2025 18:55:54 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:55:54 UTC] Raw URL: notification_center/getUnreadCount
[12-Jul-2025 18:55:54 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:55:54 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:55:54 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:55:54 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:55:54 UTC] Session check - Current time: 1752346554
[12-Jul-2025 18:55:54 UTC] Session check - Login time: 1752346554
[12-Jul-2025 18:55:54 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:55:54 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:55:54 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:55:54 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:55:54 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:55:54 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:55:54 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:55:54 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[12-Jul-2025 18:55:54 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[12-Jul-2025 18:55:54 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[12-Jul-2025 18:55:54 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[12-Jul-2025 18:55:54 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:55:54 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:55:54 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"34","archived_count":"0"}
[12-Jul-2025 18:55:57 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:55:57 UTC] Raw URL: notification/getUnread
[12-Jul-2025 18:55:57 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[12-Jul-2025 18:55:57 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[12-Jul-2025 18:55:57 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:55:57 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[12-Jul-2025 18:55:57 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[12-Jul-2025 18:55:57 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[12-Jul-2025 18:55:57 UTC] App.php: About to call method: getUnread on controller: NotificationController
[12-Jul-2025 18:55:57 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:55:57 UTC] Database::resultSet - Result count: 20
[12-Jul-2025 18:55:57 UTC] Database::resultSet - First result: {"id":"108","user_id":"3","title":"fghfghfghh [TEST]","message":"Hello Brian Correll,\n\nYou have received a message from Brian Correll regarding the show 'Summer Classic Auto Show 2025':\n\n---\nfhfghfghfgh\n---\n\nYou can reply to this message by contacting Brian <NAME_EMAIL>.\n\nThank you!","event_id":"0","event_type":"calendar_event","is_read":"0","notification_center_id":null,"created_at":"2025-07-11 11:45:06","notification_type":"push"}
[12-Jul-2025 18:55:57 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:55:57 UTC] Database::resultSet - Result count: 0
[12-Jul-2025 18:55:57 UTC] Database::resultSet - No results found
[12-Jul-2025 18:55:57 UTC] NotificationModel::getUnreadNotifications - Found 20 push, 0 toast notifications for user 3
[12-Jul-2025 18:55:57 UTC] NotificationController::getUnread - User 3 has 20 push, 0 toast notifications
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/getUnreadCount
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"15","archived_count":"0"}
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center/delete
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => delete
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] App.php: Looking for method: delete in controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] App.php: Using camelCase method: delete for URL: delete
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: delete, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: delete on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Raw URL: notification_center
[12-Jul-2025 18:56:00 UTC] Processed URL segments: Array
(
    [0] => notification_center
)

[12-Jul-2025 18:56:00 UTC] URL parsed: Array
(
    [0] => notification_center
)

[12-Jul-2025 18:56:00 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:00 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:00 UTC] Session check - Current time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Login time: 1752346560
[12-Jul-2025 18:56:00 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:00 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:00 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:00 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:00 UTC] Controller: NotificationCenterController, Method: index, Params: Array
(
)

[12-Jul-2025 18:56:00 UTC] App.php: About to call method: index on controller: NotificationCenterController
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [473] SELECT 
                    nci.*,
                    u.name as from_user_name,
                    u.email as from_user_email
                FROM notification_center_items nci
                LEFT JOIN user_messages um ON (nci.source_table = 'user_messages' AND nci.source_id = um.id)
                LEFT JOIN users u ON um.from_user_id = u.id
                WHERE nci.user_id = :user_id AND nci.is_archived = 0 ORDER BY nci.created_at DESC LIMIT :limit OFFSET :offset
Params:  3
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [6] :limit
paramno=1
name=[6] ":limit"
is_param=1
param_type=1
Key: Name: [7] :offset
paramno=2
name=[7] ":offset"
is_param=1
param_type=1

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 18
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"id":"24","user_id":"3","notification_type":"push","source_table":"user_push_notifications","source_id":"28","title":"fghfghfghh [TEST]","message":"Hello Brian Correll,\n\nYou have received a message from Brian Correll regarding the show 'Summer Classic Auto Show 2025':\n\n---\nfhfghfghfgh\n---\n\nYou can reply to this message by contacting Brian <NAME_EMAIL>.\n\nThank you!","action_url":null,"action_text":null,"metadata":null,"is_read":"1","is_archived":"0","created_at":"2025-07-11 11:45:05","read_at":"2025-07-12 17:10:38","from_user_name":null,"from_user_email":null}
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"14","archived_count":"0"}
[12-Jul-2025 18:56:00 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:00 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:56:00 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"14","archived_count":"0"}
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Raw URL: api/getSiteLogo
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => getSiteLogo
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => getSiteLogo
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => getSiteLogo
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] Endpoint: getSiteLogo, Action: index
[12-Jul-2025 18:56:01 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:01 UTC] [API] Calling ApiController::getSiteLogo
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Raw URL: notification_center/getUnreadCount
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => notification_center
    [1] => getUnreadCount
)

[12-Jul-2025 18:56:01 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:01 UTC] Retrieved Facebook session lifetime: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Session check - Current time: **********
[12-Jul-2025 18:56:01 UTC] Session check - Login time: **********
[12-Jul-2025 18:56:01 UTC] Session check - Session lifetime: 2592000
[12-Jul-2025 18:56:01 UTC] Session check - Elapsed time: 0
[12-Jul-2025 18:56:01 UTC] Session check - Is Facebook login: Yes
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:01 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:01 UTC] App.php: Looking for method: getUnreadCount in controller: NotificationCenterController
[12-Jul-2025 18:56:01 UTC] App.php: Using camelCase method: getUnreadCount for URL: getUnreadCount
[12-Jul-2025 18:56:01 UTC] Controller: NotificationCenterController, Method: getUnreadCount, Params: Array
(
)

[12-Jul-2025 18:56:01 UTC] App.php: About to call method: getUnreadCount on controller: NotificationCenterController
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"14","archived_count":"0"}
[12-Jul-2025 18:56:01 UTC] Raw URL: api/pwa/usage
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => usage
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] Endpoint: pwa, Action: usage
[12-Jul-2025 18:56:01 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[12-Jul-2025 18:56:01 UTC] [PWA] Usage data received for user 3: {"isInstalled":false,"isStandalone":false,"supportsPush":true,"timestamp":"2025-07-12T18:56:00.856Z"}
[12-Jul-2025 18:56:01 UTC] Raw URL: notification/getUnread
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[12-Jul-2025 18:56:01 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:01 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[12-Jul-2025 18:56:01 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[12-Jul-2025 18:56:01 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[12-Jul-2025 18:56:01 UTC] App.php: About to call method: getUnread on controller: NotificationController
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 13
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"id":"28","user_id":"3","title":"fghfghfghh [TEST]","message":"Hello Brian Correll,\n\nYou have received a message from Brian Correll regarding the show 'Summer Classic Auto Show 2025':\n\n---\nfhfghfghfgh\n---\n\nYou can reply to this message by contacting Brian <NAME_EMAIL>.\n\nThank you!","event_id":"0","event_type":"calendar_event","is_read":"0","notification_center_id":null,"created_at":"2025-07-11 11:45:05","notification_type":"push"}
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 0
[12-Jul-2025 18:56:01 UTC] Database::resultSet - No results found
[12-Jul-2025 18:56:01 UTC] NotificationModel::getUnreadNotifications - Found 13 push, 0 toast notifications for user 3
[12-Jul-2025 18:56:01 UTC] NotificationController::getUnread - User 3 has 13 push, 0 toast notifications
[12-Jul-2025 18:56:01 UTC] Raw URL: api/cameraBanners
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[12-Jul-2025 18:56:01 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] API endpoint called
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 4
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Raw URL: api/cameraBanners
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[12-Jul-2025 18:56:01 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] API endpoint called
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 4
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[12-Jul-2025 18:56:01 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] Raw URL: api/notifications/subscribe
[12-Jul-2025 18:56:01 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => notifications
    [2] => subscribe
)

[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => api
    [1] => notifications
    [2] => subscribe
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => notifications
    [1] => subscribe
)

[12-Jul-2025 18:56:01 UTC] [API_ROUTING] Endpoint: notifications, Action: subscribe
[12-Jul-2025 18:56:01 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:01 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[12-Jul-2025 18:56:01 UTC] [PWA] Push subscription saved for user 3
[12-Jul-2025 18:56:01 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:01 UTC] No URL parameter found or error occurred, using default route
[12-Jul-2025 18:56:01 UTC] URL parsed: Array
(
    [0] => home
    [1] => index
)

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 36
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[12-Jul-2025 18:56:01 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[12-Jul-2025 18:56:01 UTC] App.php: Looking for method: index in controller: HomeController
[12-Jul-2025 18:56:01 UTC] App.php: Using camelCase method: index for URL: index
[12-Jul-2025 18:56:01 UTC] Controller: HomeController, Method: index, Params: Array
(
)

[12-Jul-2025 18:56:01 UTC] App.php: About to call method: index on controller: HomeController
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [401] SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id 
                        WHERE s.status = :status AND s.start_date >= CURDATE() 
                        ORDER BY s.start_date ASC LIMIT :limit
Params:  2
Key: Name: [7] :status
paramno=0
name=[7] ":status"
is_param=1
param_type=2
Key: Name: [6] :limit
paramno=1
name=[6] ":limit"
is_param=1
param_type=1

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 0
[12-Jul-2025 18:56:01 UTC] Database::resultSet - No results found
[12-Jul-2025 18:56:01 UTC] Database::resultSet - Executing query: SQL: [450] SELECT
                    notification_type,
                    SUM(CASE WHEN is_read = 0 AND is_archived = 0 THEN 1 ELSE 0 END) as unread_count,
                    SUM(CASE WHEN is_archived = 0 THEN 1 ELSE 0 END) as total_count,
                    SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived_count
                FROM notification_center_items
                WHERE user_id = :user_id
                GROUP BY notification_type
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:01 UTC] Database::resultSet - Result count: 2
[12-Jul-2025 18:56:01 UTC] Database::resultSet - First result: {"notification_type":"push","unread_count":"0","total_count":"14","archived_count":"0"}
[12-Jul-2025 18:56:02 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:02 UTC] Raw URL: notification/getUnread
[12-Jul-2025 18:56:02 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[12-Jul-2025 18:56:02 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[12-Jul-2025 18:56:02 UTC] Session timestamp refreshed in Controller for user ID: 3
[12-Jul-2025 18:56:02 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[12-Jul-2025 18:56:02 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[12-Jul-2025 18:56:02 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[12-Jul-2025 18:56:02 UTC] App.php: About to call method: getUnread on controller: NotificationController
[12-Jul-2025 18:56:02 UTC] Database::resultSet - Executing query: SQL: [201] SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:02 UTC] Database::resultSet - Result count: 13
[12-Jul-2025 18:56:02 UTC] Database::resultSet - First result: {"id":"28","user_id":"3","title":"fghfghfghh [TEST]","message":"Hello Brian Correll,\n\nYou have received a message from Brian Correll regarding the show 'Summer Classic Auto Show 2025':\n\n---\nfhfghfghfgh\n---\n\nYou can reply to this message by contacting Brian <NAME_EMAIL>.\n\nThank you!","event_id":"0","event_type":"calendar_event","is_read":"0","notification_center_id":null,"created_at":"2025-07-11 11:45:05","notification_type":"push"}
[12-Jul-2025 18:56:02 UTC] Database::resultSet - Executing query: SQL: [203] SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20
Params:  1
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2

[12-Jul-2025 18:56:02 UTC] Database::resultSet - Result count: 0
[12-Jul-2025 18:56:02 UTC] Database::resultSet - No results found
[12-Jul-2025 18:56:02 UTC] NotificationModel::getUnreadNotifications - Found 13 push, 0 toast notifications for user 3
[12-Jul-2025 18:56:02 UTC] NotificationController::getUnread - User 3 has 13 push, 0 toast notifications
[12-Jul-2025 18:56:03 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:03 UTC] Raw URL: api/pwa/vapid-key
[12-Jul-2025 18:56:03 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => vapid-key
)

[12-Jul-2025 18:56:03 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => vapid-key
)

[12-Jul-2025 18:56:03 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => vapid-key
)

[12-Jul-2025 18:56:03 UTC] [API_ROUTING] Endpoint: pwa, Action: vapid-key
[12-Jul-2025 18:56:03 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[12-Jul-2025 18:56:03 UTC] Session lifetime from database: 2592000 seconds
[12-Jul-2025 18:56:03 UTC] Raw URL: api/pwa/fcm-subscribe
[12-Jul-2025 18:56:03 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => fcm-subscribe
)

[12-Jul-2025 18:56:03 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => fcm-subscribe
)

[12-Jul-2025 18:56:03 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => fcm-subscribe
)

[12-Jul-2025 18:56:03 UTC] [API_ROUTING] Endpoint: pwa, Action: fcm-subscribe
[12-Jul-2025 18:56:03 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[12-Jul-2025 18:56:03 UTC] [FCM] Saved FCM token for user 3