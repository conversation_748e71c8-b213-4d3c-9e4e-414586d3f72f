# Messaging and Notification System Documentation

## Overview

This document explains how to properly use the messaging and notification systems in the Events and Shows platform. The system has been consolidated to provide a unified approach for sending messages that support replies and deliver notifications via all user preferences.

## System Architecture

### Core Components

1. **NotificationCenterModel** - Handles MESSAGE type notifications with reply support
2. **NotificationService** - Handles delivery via user preferences (email, push, SMS, toast)
3. **Auth System** - Manages role-based permissions for messaging
4. **Reply System** - Controlled reply functionality with `[reply]` tags

### Notification Types

| Type | Description | Reply Support | Use Case |
|------|-------------|---------------|----------|
| `message` | User-to-user messages | ✅ YES | Judge communications, role assignments |
| `push` | Push notifications | ❌ NO | System alerts, announcements |
| `system` | System notifications | ❌ NO | Admin announcements |
| `toast` | In-app notifications | ❌ NO | Temporary status updates |

## How to Send Messages (CORRECT METHOD)

### ✅ Use NotificationCenterModel::sendMessage()

This is the **ONLY** method you should use for sending messages between users. It automatically:

- Creates MESSAGE type notification (supports replies)
- Sends via ALL user preferences (email, push, SMS, toast)
- Handles reply permissions and tracking
- Maintains conversation threading

```php
// Load the notification center model
require_once APPROOT . '/models/NotificationCenterModel.php';
$notificationCenterModel = new NotificationCenterModel();

// Send message
$messageId = $notificationCenterModel->sendMessage(
    $fromUserId,                    // Who is sending (required)
    $toUserId,                      // Who receives it (required)
    $subject,                       // Subject line (required)
    $message . "\n\n[reply]",      // Message content with [reply] tag (required)
    $showId,                        // Show ID for context (optional)
    false                           // requires_reply flag (optional)
);

if ($messageId) {
    // Success - message sent and notifications delivered
    echo "Message sent successfully!";
} else {
    // Failed to send
    echo "Failed to send message";
}
```

### Message Content Guidelines

#### Adding [reply] Tag
```php
// For regular users to reply once
$message = "Your message content here.\n\n[reply]";

// Admin/coordinator/judge/staff can always reply regardless of [reply] tag
```

#### Subject Line Best Practices
```php
$subject = "Judge Assignment - {$show->name}";
$subject = "Role Request - {$show->name}";
$subject = "Show Update - {$show->name}";
```

## Reply System Rules

### Who Can Reply?

1. **Admin/Coordinator/Judge/Staff** - Can reply to ANY message, ANY time
2. **Regular Users** - Can only reply to messages containing `[reply]`, and only ONCE per message

### Reply Permissions Check

```php
// Check if user can reply to a specific message
$canReply = $notificationCenterModel->canUserReplyToMessage($userId, $messageId);

if ($canReply) {
    // Show reply form
} else {
    // Show "cannot reply" message
}
```

### Role-Based Permissions

The system uses your existing Auth system:

```php
require_once APPROOT . '/core/Auth.php';
$auth = new Auth();

// Check if user has privileged role (unlimited replies)
$isPrivileged = $auth->hasRole(['admin', 'coordinator', 'judge', 'staff']);
```

## Common Use Cases

### 1. Judge Management Messages

```php
// In JudgeManagementController
public function sendMessage($showId, $judgeId) {
    // Validate inputs and get data...
    
    require_once APPROOT . '/models/NotificationCenterModel.php';
    $notificationCenterModel = new NotificationCenterModel();
    
    $result = $notificationCenterModel->sendMessage(
        $this->auth->getCurrentUserId(),  // From current user
        $judgeId,                         // To judge
        "Message from {$sender->name} regarding {$show->name}",
        $message . "\n\n[reply]",        // Add [reply] for responses
        $showId                           // Show context
    );
    
    if ($result) {
        $this->setFlashMessage('success', 'Message sent successfully to ' . $judge->name . '. They will receive notifications via their enabled preferences and can reply through the message center.', 'success');
    } else {
        $this->setFlashMessage('error', 'Failed to send message', 'danger');
    }
}
```

### 2. Role Assignment Notifications

```php
// In ShowRoleModel
public function sendRoleRequestNotification($requestId) {
    // Get request data...
    
    require_once APPROOT . '/models/NotificationCenterModel.php';
    $notificationCenterModel = new NotificationCenterModel();
    
    $subject = "Role Request - {$show->name}";
    $message = "You have been requested to be a {$role} for '{$show->name}'. Please log in to accept or decline.";
    
    return $notificationCenterModel->sendMessage(
        $request->requested_by,      // From requester
        $request->user_id,           // To user
        $subject,
        $message . "\n\n[reply]",   // Allow responses
        $request->show_id
    );
}
```

### 3. System Announcements (Admin Only)

```php
// For system-wide announcements
public function sendSystemAnnouncement($userIds, $subject, $message) {
    require_once APPROOT . '/models/NotificationCenterModel.php';
    $notificationCenterModel = new NotificationCenterModel();
    
    $systemUserId = 1; // System user ID
    
    foreach ($userIds as $userId) {
        $notificationCenterModel->sendMessage(
            $systemUserId,               // From system
            $userId,                     // To each user
            $subject,
            $message,                    // No [reply] needed for announcements
            null                         // No show context
        );
    }
}
```

## What Users Receive

When you send a message using `NotificationCenterModel::sendMessage()`, the recipient gets:

### In Notification Center
- ✅ MESSAGE type notification in "Messages" tab
- ✅ Reply form (always for admin, with `[reply]` for regular users)
- ✅ Conversation threading
- ✅ Read/unread status

### Via Their Notification Preferences
- ✅ **Email** (if enabled) - Full message content
- ✅ **Push Notification** (if enabled) - Title and preview
- ✅ **SMS** (if enabled) - Text message
- ✅ **Toast Notification** (if enabled) - In-app popup

## Migration from Old Methods

### ❌ WRONG - Don't Use These Anymore

```php
// OLD - Creates wrong notification types
$this->sendMultiTypeNotification($userId, $subject, $message);

// OLD - Only sends email
$this->queueNotification($userId, 'email', $subject, $message);

// OLD - Redundant dual sending
$notificationCenterModel->sendMessage(...);
$this->sendMultiTypeNotification(...); // REMOVE THIS
```

### ✅ CORRECT - Use This Instead

```php
// NEW - One method does everything
$notificationCenterModel->sendMessage(
    $fromUserId,
    $toUserId,
    $subject,
    $message . "\n\n[reply]",
    $showId
);
```

## Error Handling

```php
try {
    $messageId = $notificationCenterModel->sendMessage(
        $fromUserId,
        $toUserId,
        $subject,
        $message . "\n\n[reply]",
        $showId
    );
    
    if ($messageId) {
        // Success
        $this->setFlashMessage('success', 'Message sent successfully', 'success');
    } else {
        // Failed
        $this->setFlashMessage('error', 'Failed to send message', 'danger');
    }
    
} catch (Exception $e) {
    error_log("Error sending message: " . $e->getMessage());
    $this->setFlashMessage('error', 'An error occurred while sending the message', 'danger');
}
```

## Checking User Notification Preferences

```php
// Check if user can send notifications (has at least one type enabled)
$canSend = $notificationCenterModel->canUserSendNotifications($userId);

if (!$canSend) {
    $this->setFlashMessage('error', 'You cannot send messages because all your notification types are disabled. Please enable at least one notification type in your settings.', 'warning');
    return;
}
```

## Testing the System

### 1. Send Test Message
```php
// Use the test script
// Visit: http://yoursite.com/send_test_message.php
```

### 2. Check Message Center
```php
// Visit: http://yoursite.com/notification_center
// Look in "Messages" tab (not Push/System/Toast)
```

### 3. Verify Reply Functionality
```php
// As admin: Should always see reply form
// As regular user: Should see reply form only on [reply] messages
```

## Best Practices

1. **Always use `NotificationCenterModel::sendMessage()`** for user-to-user communication
2. **Add `[reply]` tag** when you want regular users to be able to respond
3. **Include show context** when relevant for better organization
4. **Use descriptive subjects** that include show name or context
5. **Handle errors gracefully** with try-catch blocks
6. **Check user permissions** before sending messages
7. **Test with different user roles** to verify reply permissions

## Troubleshooting

### Reply Form Not Showing
- Check if notification is MESSAGE type (not push/system/toast)
- Verify user has admin/coordinator/judge/staff role OR message contains `[reply]`
- Check if user has already used their one reply (for regular users)

### Notifications Not Delivered
- Verify user has enabled notification preferences
- Check error logs for delivery failures
- Ensure `NotificationService` is properly configured

### Permission Issues
- Verify user roles in Auth system
- Check if user has required permissions to send messages
- Ensure session data is current

## Summary

**Use `NotificationCenterModel::sendMessage()` for ALL user-to-user messaging. It handles everything automatically: MESSAGE type creation, reply support, and delivery via all user preferences.**

This unified approach ensures consistent messaging, proper reply functionality, and respect for user notification preferences across the entire platform.
