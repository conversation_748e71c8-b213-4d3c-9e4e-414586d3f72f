/**
 * Firebase Messaging Service Worker
 * 
 * This file is required by Firebase SDK but we redirect all functionality
 * to the main service worker to avoid conflicts.
 */

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
    authDomain: "rowaneliterides.firebaseapp.com",
    projectId: "rowaneliterides",
    storageBucket: "rowaneliterides.firebasestorage.app",
    messagingSenderId: "310533125467",
    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
    console.log('[FCM-SW] Received background message:', payload);
    
    const notificationTitle = payload.notification?.title || 'New Notification';
    const notificationOptions = {
        body: payload.notification?.body || 'You have a new message',
        icon: payload.notification?.icon || '/public/images/icon-192x192.png',
        badge: '/public/images/icon-72x72.png',
        tag: payload.data?.tag || 'general',
        data: payload.data || {},
        actions: [
            {
                action: 'view',
                title: 'View',
                icon: '/public/images/icon-72x72.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss'
            }
        ],
        requireInteraction: true,
        vibrate: [200, 100, 200]
    };
    
    return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
    console.log('[FCM-SW] Notification click received:', event);
    
    event.notification.close();
    
    if (event.action === 'view' || !event.action) {
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
                for (const client of clientList) {
                    if (client.url.includes(self.location.origin) && 'focus' in client) {
                        return client.focus();
                    }
                }
                
                if (clients.openWindow) {
                    const targetUrl = event.notification.data?.url || '/';
                    return clients.openWindow(targetUrl);
                }
            })
        );
    }
});

// Original code below (commented out)
/*

// Original code below (commented out)
/*
// Import Firebase scripts for service worker
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
    authDomain: "rowaneliterides.firebaseapp.com",
    projectId: "rowaneliterides",
    storageBucket: "rowaneliterides.firebasestorage.app",
    messagingSenderId: "310533125467",
    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
    console.log('[firebase-messaging-sw.js] Received background message:', payload);
    
    const { notification, data } = payload;
    
    // Customize notification
    const notificationTitle = notification?.title || 'RER Events';
    const notificationOptions = {
        body: notification?.body || 'You have a new notification',
        icon: notification?.icon || '/public/images/icons/icon-192x192.png',
        badge: '/public/images/icons/badge-72x72.png',
        tag: data?.tag || 'fcm-notification',
        requireInteraction: false,
        actions: [],
        data: {
            url: data?.url || '/',
            ...data
        }
    };
    
    // Add actions based on notification type
    if (data?.type) {
        switch (data.type) {
            case 'event_reminder':
                notificationOptions.actions = [
                    { action: 'view', title: 'View Event' },
                    { action: 'dismiss', title: 'Dismiss' }
                ];
                break;
            case 'registration_update':
                notificationOptions.actions = [
                    { action: 'view_registration', title: 'View Registration' },
                    { action: 'dashboard', title: 'Dashboard' }
                ];
                break;
            case 'judging_reminder':
                notificationOptions.actions = [
                    { action: 'judge', title: 'Start Judging' },
                    { action: 'later', title: 'Remind Later' }
                ];
                break;
        }
    }
    
    // Show notification
    return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
    console.log('[firebase-messaging-sw.js] Notification click received:', event);
    
    event.notification.close();
    
    const { action, data } = event;
    let url = data?.url || '/';
    
    // Handle different actions
    switch (action) {
        case 'view':
        case 'view_registration':
            // Use the URL from notification data
            break;
        case 'dashboard':
            url = '/user/dashboard';
            break;
        case 'judge':
            url = data?.judge_url || '/judge/dashboard';
            break;
        case 'later':
            // Don't open anything, just close
            return;
        case 'dismiss':
            // Don't open anything, just close
            return;
        default:
            // Default action (click on notification body)
            break;
    }
    
    // Open the URL
    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
            // Check if there's already a window/tab open with the target URL
            for (const client of clientList) {
                if (client.url === url && 'focus' in client) {
                    return client.focus();
                }
            }
            
            // If no existing window/tab, open a new one
            if (clients.openWindow) {
                return clients.openWindow(url);
            }
        })
    );
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
    console.log('[firebase-messaging-sw.js] Notification closed:', event);
    
    // You can track notification dismissals here if needed
    const { data } = event.notification;
    
    if (data?.track_dismissal) {
        // Send dismissal tracking to your server
        fetch('/api/pwa/track-dismissal', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                notification_id: data.notification_id,
                action: 'dismissed'
            })
        }).catch(err => {
            console.log('Failed to track notification dismissal:', err);
        });
    }
});

// Handle push subscription changes
self.addEventListener('pushsubscriptionchange', (event) => {
    console.log('[firebase-messaging-sw.js] Push subscription changed:', event);
    
    // Handle subscription changes - refresh the token
    event.waitUntil(
        messaging.getToken().then((token) => {
            if (token) {
                // Send new token to server
                return fetch('/api/pwa/fcm-subscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fcm_token: token,
                        user_agent: navigator.userAgent,
                        timestamp: new Date().toISOString(),
                        subscription_change: true
                    })
                });
            }
        }).catch(err => {
            console.log('Failed to handle subscription change:', err);
        })
    );
});

*/