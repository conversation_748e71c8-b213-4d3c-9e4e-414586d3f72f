<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="btn btn-outline-secondary btn-sm me-3">
                        <i class="fas fa-arrow-left me-1"></i>Back to Notifications
                    </a>
                    <h1 class="h3 mb-0 d-inline">View Notification</h1>
                </div>
            </div>

            <!-- Notification Details -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <?php
                                $iconClass = 'fas fa-bell';
                                $iconColor = 'text-primary';
                                
                                switch ($notification->notification_type) {
                                    case 'message':
                                        $iconClass = 'fas fa-envelope';
                                        $iconColor = 'text-success';
                                        break;
                                    case 'push':
                                        $iconClass = 'fas fa-mobile-alt';
                                        $iconColor = 'text-info';
                                        break;
                                    case 'system':
                                        $iconClass = 'fas fa-cog';
                                        $iconColor = 'text-warning';
                                        break;
                                    case 'judging':
                                        $iconClass = 'fas fa-gavel';
                                        $iconColor = 'text-purple';
                                        break;
                                    case 'event':
                                        $iconClass = 'fas fa-calendar';
                                        $iconColor = 'text-primary';
                                        break;
                                }
                                ?>
                                <i class="<?php echo $iconClass; ?> <?php echo $iconColor; ?> me-2"></i>
                                <?php echo htmlspecialchars($notification->title); ?>
                            </h5>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo date('M j, Y g:i A', strtotime($notification->created_at)); ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if ($notification->from_user_name): ?>
                        <div class="mb-3">
                            <strong>From:</strong> <?php echo htmlspecialchars($notification->from_user_name); ?>
                            <?php if ($notification->from_user_email): ?>
                                <small class="text-muted">(<?php echo htmlspecialchars($notification->from_user_email); ?>)</small>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="notification-message">
                        <?php echo nl2br(htmlspecialchars($notification->message)); ?>
                    </div>
                    
                    <?php if ($notification->metadata): ?>
                        <?php $metadata = json_decode($notification->metadata, true); ?>
                        <?php if ($metadata && isset($metadata['show_id']) && $metadata['show_id']): ?>
                            <div class="mt-3 p-3 bg-light rounded">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    This message is related to a car show.
                                </small>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Conversation Thread (for messages) -->
            <?php if ($conversation && count($conversation) > 0): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>Conversation Thread
                            <span class="badge bg-secondary ms-2"><?php echo count($conversation); ?> message<?php echo count($conversation) > 1 ? 's' : ''; ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($conversation as $index => $message): ?>
                            <div class="message-item <?php echo $index > 0 ? 'mt-3 pt-3 border-top' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div class="d-flex align-items-center">
                                        <?php if ($message->from_user_id == $_SESSION['user_id']): ?>
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($message->from_user_name); ?></strong>
                                                <span class="badge bg-primary ms-1">You</span>
                                            </div>
                                        <?php else: ?>
                                            <div class="avatar-circle bg-success text-white me-2">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($message->from_user_name); ?></strong>
                                                <span class="badge bg-success ms-1">Sender</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('M j, Y g:i A', strtotime($message->created_at)); ?>
                                    </small>
                                </div>
                                <div class="message-content ms-5">
                                    <div class="bg-light p-3 rounded">
                                        <?php echo nl2br(htmlspecialchars($message->message)); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="mt-3 pt-3 border-top">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                <strong>Privacy Note:</strong> Email addresses are never shared between users.
                                Replies are sent using the recipient's notification preferences.
                            </small>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Reply Form (for messages) -->
            <?php if ($notification->notification_type === 'message'): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-reply me-2"></i>Send Reply
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Check if user can send notifications
                        require_once APPROOT . '/models/NotificationCenterModel.php';
                        $notificationCenterModel = new NotificationCenterModel();
                        $canSendNotifications = $notificationCenterModel->canUserSendNotifications($_SESSION['user_id']);
                        ?>

                        <?php if (!$canSendNotifications): ?>
                            <div class="alert alert-warning">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                                    <div>
                                        <h6 class="alert-heading mb-1">Cannot Send Reply</h6>
                                        <p class="mb-2">You cannot send replies because all your notification types are disabled.</p>
                                        <p class="mb-0">
                                            <strong>To send replies:</strong> Enable at least one notification type in your
                                            <a href="<?php echo BASE_URL; ?>/user/settings" class="alert-link">account settings</a>.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <form action="<?php echo BASE_URL; ?>/notification_center/reply" method="POST">
                                <input type="hidden" name="parent_message_id" value="<?php echo $notification->source_id; ?>">

                                <div class="mb-3">
                                    <label for="message" class="form-label">Your Reply</label>
                                    <textarea class="form-control" id="message" name="message" rows="4"
                                              placeholder="Type your reply here..." required></textarea>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Your reply will be sent as a notification to the sender using their notification preferences.
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                        Cancel
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>Send Reply
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="mt-4 text-center">
                <?php if ($notification->action_url && $notification->action_url !== "/notification_center/view/{$notification->id}"): ?>
                    <a href="<?php echo BASE_URL . $notification->action_url; ?>" class="btn btn-primary me-2">
                        <i class="fas fa-external-link-alt me-1"></i>
                        <?php echo $notification->action_text ?: 'View Related Item'; ?>
                    </a>
                <?php endif; ?>
                
                <button type="button" class="btn btn-outline-secondary" 
                        onclick="archiveNotification(<?php echo $notification->id; ?>)">
                    <i class="fas fa-archive me-1"></i>Archive
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.notification-message {
    font-size: 1.1rem;
    line-height: 1.6;
}

.message-item {
    position: relative;
}

.message-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.text-purple {
    color: #6f42c1 !important;
}

/* Avatar circles for conversation */
.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

/* Message content styling */
.message-content .bg-light {
    border-left: 3px solid #007bff;
}

.message-item:hover .message-content .bg-light {
    background-color: #f8f9fa !important;
    border-left-color: #0056b3;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>

<script>
function archiveNotification(notificationId) {
    if (!confirm('Archive this notification?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '<?php echo BASE_URL; ?>/notification_center';
        } else {
            alert('Failed to archive: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to archive notification');
    });
}

// Auto-resize textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('message');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
});
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
