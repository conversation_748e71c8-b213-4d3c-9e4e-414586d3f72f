<?php
/**
 * Safe Notification Audit
 * 
 * READ-ONLY script to understand current notification system without making changes
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load core classes
require_once 'core/Database.php';

echo "<h1>Safe Notification System Audit</h1>";
echo "<p><strong>⚠️ READ-ONLY ANALYSIS - NO CHANGES WILL BE MADE</strong></p>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to run audit.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $db = new Database();
    
    echo "<p><strong>Auditing for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Current Notification Tables Overview</h2>";
    
    // Check all notification-related tables
    $tables = [
        'notification_center_items',

        'user_push_notifications',
        'user_toast_notifications',
        'notification_queue',
        'user_notification_preferences'
    ];
    
    foreach ($tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as total, COUNT(CASE WHEN user_id = :user_id THEN 1 END) as for_user FROM {$table}");
            $db->bind(':user_id', $userId);
            $result = $db->single();
            
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
            echo "<strong>{$table}:</strong> {$result->total} total records, {$result->for_user} for you";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
            echo "<strong>{$table}:</strong> ❌ Error: " . $e->getMessage();
            echo "</div>";
        }
    }
    
    echo "<h2>2. User Messages Check</h2>";

    // Check user_messages with correct column names
    try {
        $db->query("SELECT COUNT(*) as total, COUNT(CASE WHEN to_user_id = :user_id THEN 1 END) as for_user FROM user_messages");
        $db->bind(':user_id', $userId);
        $result = $db->single();

        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<strong>user_messages:</strong> {$result->total} total records, {$result->for_user} for you (using to_user_id)";
        echo "</div>";

        // Show recent messages for this user
        if ($result->for_user > 0) {
            $db->query("SELECT * FROM user_messages WHERE to_user_id = :user_id ORDER BY created_at DESC LIMIT 3");
            $db->bind(':user_id', $userId);
            $userMessages = $db->resultSet();

            echo "<h3>Your Recent Messages:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>From User</th>";
            echo "<th style='padding: 8px;'>Subject</th>";
            echo "<th style='padding: 8px;'>Allows Reply</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";

            foreach ($userMessages as $message) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$message->id}</td>";
                echo "<td style='padding: 8px;'>{$message->from_user_id}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($message->subject, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>" . ($message->allows_reply ? 'Yes' : 'No') . "</td>";
                echo "<td style='padding: 8px;'>{$message->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "<strong>user_messages:</strong> ❌ Error: " . $e->getMessage();
        echo "</div>";
    }

    echo "<h2>3. Your Current Notifications by Type</h2>";
    
    // Check what types of notifications you currently have
    try {
        $db->query("SELECT 
                        notification_type, 
                        COUNT(*) as count,
                        COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
                        MAX(created_at) as latest
                     FROM notification_center_items 
                     WHERE user_id = :user_id 
                     GROUP BY notification_type 
                     ORDER BY count DESC");
        $db->bind(':user_id', $userId);
        $notificationTypes = $db->resultSet();
        
        if (!empty($notificationTypes)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Total</th>";
            echo "<th style='padding: 8px;'>Unread</th>";
            echo "<th style='padding: 8px;'>Latest</th>";
            echo "</tr>";
            
            foreach ($notificationTypes as $type) {
                echo "<tr>";
                echo "<td style='padding: 8px;'><strong>{$type->notification_type}</strong></td>";
                echo "<td style='padding: 8px;'>{$type->count}</td>";
                echo "<td style='padding: 8px;'>{$type->unread}</td>";
                echo "<td style='padding: 8px;'>{$type->latest}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No notifications found in notification_center_items for your user ID</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking notification types: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. Recent Toast Notifications (What's Working)</h2>";
    
    // Check recent toast notifications since those are working
    try {
        $db->query("SELECT * FROM user_toast_notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $toastNotifications = $db->resultSet();
        
        if (!empty($toastNotifications)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Title</th>";
            echo "<th style='padding: 8px;'>Message</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "<th style='padding: 8px;'>Notification Center ID</th>";
            echo "</tr>";
            
            foreach ($toastNotifications as $toast) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$toast->id}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($toast->title, 0, 20)) . "...</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($toast->message, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>{$toast->created_at}</td>";
                echo "<td style='padding: 8px;'>" . ($toast->notification_center_id ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>Analysis:</strong> Toast notifications are working. Check if they have notification_center_id links.</p>";
        } else {
            echo "<p>❌ No toast notifications found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking toast notifications: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Notification Preferences Analysis</h2>";
    
    // Check user's notification preferences
    try {
        $db->query("SELECT * FROM user_notification_preferences WHERE user_id = :user_id");
        $db->bind(':user_id', $userId);
        $preferences = $db->single();
        
        if ($preferences) {
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Your Notification Preferences:</h4>";
            echo "<ul>";
            echo "<li><strong>Email:</strong> " . ($preferences->email_notifications ? '✅ Enabled' : '❌ Disabled') . "</li>";
            echo "<li><strong>Push:</strong> " . ($preferences->push_notifications ? '✅ Enabled' : '❌ Disabled') . "</li>";
            echo "<li><strong>Toast:</strong> " . ($preferences->toast_notifications ? '✅ Enabled' : '❌ Disabled') . "</li>";
            echo "<li><strong>SMS:</strong> " . ($preferences->sms_notifications ? '✅ Enabled' : '❌ Disabled') . "</li>";
            echo "</ul>";
            echo "</div>";
            
            // Check if any are enabled
            $hasAnyEnabled = ($preferences->email_notifications || $preferences->push_notifications || 
                             $preferences->toast_notifications || $preferences->sms_notifications);
            
            if (!$hasAnyEnabled) {
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>⚠️ All Notifications Disabled</h4>";
                echo "<p>You have all notification types disabled. This might explain why you're only getting toast notifications.</p>";
                echo "</div>";
            }
        } else {
            echo "<p>❌ No notification preferences found - this might be the issue!</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking preferences: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. Working vs Broken Analysis</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ What's Currently Working:</h4>";
    echo "<ul>";
    echo "<li>Toast notifications are being sent and received</li>";
    echo "<li>Database tables exist and have data</li>";
    echo "<li>User preferences are configured</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ What's Not Working:</h4>";
    echo "<ul>";
    echo "<li>Messages not appearing in notification center</li>";
    echo "<li>Email notifications not being sent</li>";
    echo "<li>Push notifications not being sent</li>";
    echo "<li>Only toast notifications working</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>7. Safe Recommendations</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔧 Minimal Fixes Needed:</h4>";
    echo "<ol>";
    echo "<li><strong>Fix notification delivery</strong> - Use existing working toast system for other types</li>";
    echo "<li><strong>Fix notification center display</strong> - Ensure it reads from correct tables</li>";
    echo "<li><strong>Don't duplicate data</strong> - Use existing tables and sync mechanisms</li>";
    echo "<li><strong>Don't break existing</strong> - Keep current toast notifications working</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>8. Current System Analysis</h2>";
    
    // Check what notification system is currently being used
    try {
        echo "<h3>Recent Notification Queue Entries:</h3>";
        $db->query("SELECT * FROM notification_queue WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $queueEntries = $db->resultSet();
        
        if (!empty($queueEntries)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Subject</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";
            
            foreach ($queueEntries as $entry) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$entry->id}</td>";
                echo "<td style='padding: 8px;'>{$entry->notification_type}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($entry->subject, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>{$entry->status}</td>";
                echo "<td style='padding: 8px;'>{$entry->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No entries in notification_queue</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking notification queue: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>9. Next Steps (Safe)</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🎯 Recommended Safe Actions:</h4>";
    echo "<ol>";
    echo "<li><strong>Use existing notification system</strong> instead of creating new one</li>";
    echo "<li><strong>Fix sendMessage() to use working toast system</strong> for all notification types</li>";
    echo "<li><strong>Ensure notification center</strong> reads from existing data</li>";
    echo "<li><strong>Test with single message</strong> before changing judge management</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>10. Navigation</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Safe tools:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Notification Center</a> (see current state)</li>";
    echo "<li><a href='" . BASE_URL . "/user/settings' target='_blank'>User Settings</a> (check notification preferences)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
