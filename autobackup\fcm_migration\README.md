# FCM Migration Backup

This directory contains backups of files modified during the FCM (Firebase Cloud Messaging) migration.

## Migration Date
January 27, 2025

## What Changed
- Migrated from Web Push API with VAPID to FCM HTTP v1 API
- Updated NotificationModel to use FCM tokens instead of Web Push subscriptions
- Added new FCM JavaScript manager and Firebase service worker
- Created new API endpoints for FCM token management

## Backup Contents
- `NotificationModel_original.php` - Original NotificationModel before FCM changes
- `pwa-features_original.js` - Original PWA features before FCM integration
- `header_original.php` - Original header before Firebase SDK inclusion

## Rollback Instructions
If you need to rollback to the old Web Push API system:

1. Restore the original files from this backup
2. Remove the new FCM files:
   - `/public/js/fcm-notifications.js`
   - `/firebase-messaging-sw.js`
   - `/api/notifications/fcm-subscribe.php`
3. Remove Firebase SDK scripts from header.php
4. Optionally drop the `fcm_tokens` table

## Migration Benefits
- Better notification delivery rates
- More reliable push notifications
- Better integration with Google services
- Improved error handling and token management
- Support for rich notifications and actions

## Support
See `FCM_SETUP_GUIDE.md` for complete setup instructions.