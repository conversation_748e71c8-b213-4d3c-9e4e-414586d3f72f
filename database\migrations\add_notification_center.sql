-- Notification Center Database Migration
-- This creates the necessary tables for the notification center system

-- Create user_messages table for direct messages between users
CREATE TABLE IF NOT EXISTS `user_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_user_id` int(11) NOT NULL,
  `to_user_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `show_id` int(11) DEFAULT NULL COMMENT 'Related show if applicable',
  `parent_message_id` int(11) DEFAULT NULL COMMENT 'For reply threading',
  `requires_reply` tinyint(1) DEFAULT 0 COMMENT 'Whether sender expects a reply',
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_to_user` (`to_user_id`),
  KEY `idx_from_user` (`from_user_id`),
  <PERSON>EY `idx_show` (`show_id`),
  <PERSON><PERSON>Y `idx_parent` (`parent_message_id`),
  KEY `idx_unread` (`to_user_id`, `is_read`),
  KEY `idx_created` (`created_at` DESC),
  FOREIGN KEY (`from_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`to_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_message_id`) REFERENCES `user_messages`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notification_center_items table for unified notification display
CREATE TABLE IF NOT EXISTS `notification_center_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `notification_type` enum('message','push','toast','system','event','judging') NOT NULL,
  `source_table` varchar(50) DEFAULT NULL COMMENT 'Source table name (user_messages, user_push_notifications, etc)',
  `source_id` int(11) DEFAULT NULL COMMENT 'ID in source table',
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `action_url` varchar(500) DEFAULT NULL COMMENT 'URL to navigate to when clicked',
  `action_text` varchar(100) DEFAULT NULL COMMENT 'Text for action button',
  `metadata` json DEFAULT NULL COMMENT 'Additional data for the notification',
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_id`, `notification_type`),
  KEY `idx_user_unread` (`user_id`, `is_read`),
  KEY `idx_source` (`source_table`, `source_id`),
  KEY `idx_created` (`created_at` DESC),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add columns to existing notification tables to link to notification center
ALTER TABLE `user_push_notifications` 
ADD COLUMN `notification_center_id` int(11) DEFAULT NULL AFTER `is_read`,
ADD KEY `idx_notification_center` (`notification_center_id`);

ALTER TABLE `user_toast_notifications` 
ADD COLUMN `notification_center_id` int(11) DEFAULT NULL AFTER `is_read`,
ADD KEY `idx_notification_center` (`notification_center_id`);

-- Add metadata column to notification_queue for better context
ALTER TABLE `notification_queue` 
ADD COLUMN `metadata` json DEFAULT NULL COMMENT 'Additional context data' AFTER `notification_data`;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_notification_queue_user_type` ON `notification_queue` (`user_id`, `notification_type`);
CREATE INDEX IF NOT EXISTS `idx_notification_queue_status` ON `notification_queue` (`status`, `scheduled_for`);
