<?php
/**
 * Sync Notification Center
 * 
 * This script syncs existing notifications to the notification center
 * and sets up the new notification system.
 */

// Include the configuration
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Notification Center Sync</h1>";

try {
    $db = new Database();
    $notificationCenterModel = new NotificationCenterModel();
    
    echo "<h2>1. Running Database Migration</h2>";
    
    // Run the migration
    $migrationFile = 'database/migrations/add_notification_center.sql';
    if (file_exists($migrationFile)) {
        $sql = file_get_contents($migrationFile);
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                try {
                    $db->query($statement);
                    $db->execute();
                    echo "<p>✅ Executed: " . substr($statement, 0, 50) . "...</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ Warning: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<p><strong>✅ Database migration completed</strong></p>";
    } else {
        echo "<p>❌ Migration file not found: $migrationFile</p>";
    }
    
    echo "<h2>2. Syncing Existing Notifications</h2>";
    
    // Get all users
    $db->query("SELECT id FROM users WHERE status = 'active'");
    $users = $db->resultSet();
    
    $totalSynced = 0;
    
    foreach ($users as $user) {
        echo "<h3>Syncing notifications for User ID: {$user->id}</h3>";
        
        // Sync existing push notifications
        $sql = "INSERT IGNORE INTO notification_center_items 
                (user_id, notification_type, source_table, source_id, title, message, is_read, created_at)
                SELECT 
                    user_id, 
                    'push' as notification_type,
                    'user_push_notifications' as source_table,
                    id as source_id,
                    title,
                    message,
                    is_read,
                    created_at
                FROM user_push_notifications 
                WHERE user_id = :user_id";
        
        $db->query($sql);
        $db->bind(':user_id', $user->id);
        $db->execute();
        $pushSynced = $db->rowCount();
        
        // Sync existing toast notifications
        $sql = "INSERT IGNORE INTO notification_center_items 
                (user_id, notification_type, source_table, source_id, title, message, is_read, created_at)
                SELECT 
                    user_id, 
                    'toast' as notification_type,
                    'user_toast_notifications' as source_table,
                    id as source_id,
                    title,
                    message,
                    is_read,
                    created_at
                FROM user_toast_notifications 
                WHERE user_id = :user_id";
        
        $db->query($sql);
        $db->bind(':user_id', $user->id);
        $db->execute();
        $toastSynced = $db->rowCount();
        
        $userTotal = $pushSynced + $toastSynced;
        $totalSynced += $userTotal;
        
        echo "<p>User {$user->id}: {$pushSynced} push + {$toastSynced} toast = {$userTotal} notifications synced</p>";
    }
    
    echo "<p><strong>✅ Total notifications synced: {$totalSynced}</strong></p>";
    
    echo "<h2>3. Testing Notification Center</h2>";
    
    // Test creating a notification center item
    if (!empty($users)) {
        $testUserId = $users[0]->id;
        
        $testNotificationId = $notificationCenterModel->createNotificationItem(
            $testUserId,
            'system',
            'Notification Center Setup Complete',
            'Your notification center has been successfully set up and is ready to use!',
            null,
            null,
            '/notification_center',
            'View Center',
            ['setup' => true]
        );
        
        if ($testNotificationId) {
            echo "<p>✅ Test notification created successfully (ID: {$testNotificationId})</p>";
        } else {
            echo "<p>❌ Failed to create test notification</p>";
        }
    }
    
    echo "<h2>4. Verification</h2>";
    
    // Check notification center items count
    $db->query("SELECT COUNT(*) as count FROM notification_center_items");
    $result = $db->single();
    $totalItems = $result->count;
    
    echo "<p>Total notification center items: <strong>{$totalItems}</strong></p>";
    
    // Check by type
    $db->query("SELECT notification_type, COUNT(*) as count FROM notification_center_items GROUP BY notification_type");
    $typeResults = $db->resultSet();
    
    echo "<p>Breakdown by type:</p>";
    echo "<ul>";
    foreach ($typeResults as $typeResult) {
        echo "<li>{$typeResult->notification_type}: {$typeResult->count}</li>";
    }
    echo "</ul>";
    
    echo "<h2>✅ Notification Center Setup Complete!</h2>";
    echo "<p>You can now access the notification center at: <a href='" . BASE_URL . "/notification_center'>" . BASE_URL . "/notification_center</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
