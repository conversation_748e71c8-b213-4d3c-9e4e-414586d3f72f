<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bell me-2"></i>Notification Center
                    <?php if ($counts['total_unread'] > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $counts['total_unread']; ?></span>
                    <?php endif; ?>
                </h1>
                
                <div class="btn-group" role="group">
                    <?php if ($counts['total_unread'] > 0): ?>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-1"></i>Mark All Read
                        </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshNotifications()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Filter Tabs -->
            <ul class="nav nav-tabs mb-3" id="notificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'all' && $current_status !== 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?type=all&status=all">
                        All
                        <?php if ($counts['total_unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['total_unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'message' && $current_status !== 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?type=message&status=all">
                        Messages
                        <?php if ($counts['message']['unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['message']['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'push' && $current_status !== 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?type=push&status=all">
                        Push
                        <?php if ($counts['push']['unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['push']['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'system' && $current_status !== 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?type=system&status=all">
                        System
                        <?php if ($counts['system']['unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['system']['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_status === 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?type=all&status=archived">
                        <i class="fas fa-archive me-1"></i>Archived
                        <?php
                        $archivedCount = $counts['archived_count'] ?? 0;
                        if ($archivedCount > 0): ?>
                            <span class="badge bg-secondary ms-1"><?php echo $archivedCount; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            </ul>

            <!-- Status Filter and Bulk Actions -->
            <?php if ($current_status !== 'archived'): ?>
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=all"
                           class="btn <?php echo $current_status === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            All
                        </a>
                        <a href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=unread"
                           class="btn <?php echo $current_status === 'unread' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            Unread
                        </a>
                        <a href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=read"
                           class="btn <?php echo $current_status === 'read' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            Read
                        </a>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div id="bulk-actions" style="display: none;">
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-success" onclick="bulkMarkAsRead()">
                                <i class="fas fa-check me-1"></i>Mark Read
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="bulkArchive()">
                                <i class="fas fa-archive me-1"></i>Hide
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearSelection()">
                            <i class="fas fa-times me-1"></i>Clear
                        </button>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-3">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Viewing archived notifications. These are hidden from your main notification list.
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="alert-link ms-2">Back to Active Notifications</a>
                </div>
            </div>
            <?php endif; ?>

            <!-- Notifications List -->
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No notifications found</h4>
                    <p class="text-muted">
                        <?php if ($current_status === 'unread'): ?>
                            You have no unread notifications.
                        <?php elseif ($current_type !== 'all'): ?>
                            No <?php echo $current_type; ?> notifications found.
                        <?php else: ?>
                            You don't have any notifications yet.
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="notification-list">
                    <?php if (!empty($notifications) && $current_status !== 'archived'): ?>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            <label class="form-check-label" for="select-all">
                                <small class="text-muted">Select all notifications</small>
                            </label>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php foreach ($notifications as $notification): ?>
                        <div class="card mb-2 notification-item <?php echo $notification->is_read ? '' : 'notification-unread'; ?>"
                             data-notification-id="<?php echo $notification->id; ?>">
                            <div class="card-body py-3">
                                <div class="row align-items-center">
                                    <?php if ($current_status !== 'archived'): ?>
                                    <div class="col-auto">
                                        <div class="form-check">
                                            <input class="form-check-input notification-checkbox" type="checkbox"
                                                   value="<?php echo $notification->id; ?>"
                                                   onchange="updateBulkActions()">
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <div class="col">
                                        <div class="d-flex align-items-start">
                                            <!-- Notification Icon -->
                                            <div class="notification-icon me-3">
                                                <?php
                                                $iconClass = 'fas fa-bell';
                                                $iconColor = 'text-primary';
                                                
                                                switch ($notification->notification_type) {
                                                    case 'message':
                                                        $iconClass = 'fas fa-envelope';
                                                        $iconColor = 'text-success';
                                                        break;
                                                    case 'push':
                                                        $iconClass = 'fas fa-mobile-alt';
                                                        $iconColor = 'text-info';
                                                        break;
                                                    case 'system':
                                                        $iconClass = 'fas fa-cog';
                                                        $iconColor = 'text-warning';
                                                        break;
                                                    case 'judging':
                                                        $iconClass = 'fas fa-gavel';
                                                        $iconColor = 'text-purple';
                                                        break;
                                                    case 'event':
                                                        $iconClass = 'fas fa-calendar';
                                                        $iconColor = 'text-primary';
                                                        break;
                                                }
                                                ?>
                                                <i class="<?php echo $iconClass; ?> <?php echo $iconColor; ?> fa-lg"></i>
                                            </div>
                                            
                                            <!-- Notification Content -->
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($notification->title); ?>
                                                    <?php if (!$notification->is_read): ?>
                                                        <span class="badge bg-danger ms-2">New</span>
                                                    <?php endif; ?>
                                                </h6>
                                                
                                                <?php if ($notification->from_user_name): ?>
                                                    <small class="text-muted d-block mb-1">
                                                        From: <?php echo htmlspecialchars($notification->from_user_name); ?>
                                                    </small>
                                                <?php endif; ?>
                                                
                                                <p class="mb-2 text-muted">
                                                    <?php 
                                                    $message = htmlspecialchars($notification->message);
                                                    echo strlen($message) > 150 ? substr($message, 0, 150) . '...' : $message;
                                                    ?>
                                                </p>
                                                
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo date('M j, Y g:i A', strtotime($notification->created_at)); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="col-auto">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <?php if ($notification->action_url): ?>
                                                <a href="<?php echo BASE_URL . $notification->action_url; ?>"
                                                   class="btn btn-outline-primary btn-sm" title="<?php echo $notification->action_text ?: 'View'; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="<?php echo BASE_URL; ?>/notification_center/viewNotification/<?php echo $notification->id; ?>"
                                                   class="btn btn-outline-primary btn-sm" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if (!$notification->is_read): ?>
                                                <button type="button" class="btn btn-outline-success btn-sm"
                                                        onclick="markAsRead(<?php echo $notification->id; ?>)" title="Mark as Read">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>

                                            <?php if ($current_status === 'archived'): ?>
                                                <button type="button" class="btn btn-outline-info btn-sm"
                                                        onclick="unarchiveNotification(<?php echo $notification->id; ?>)" title="Restore">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteNotification(<?php echo $notification->id; ?>)" title="Delete Permanently">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                        onclick="archiveNotification(<?php echo $notification->id; ?>)" title="Hide">
                                                    <i class="fas fa-archive"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteNotification(<?php echo $notification->id; ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Notifications pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ((int)$current_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=<?php echo $current_status; ?>&page=<?php echo (int)$current_page - 1; ?>">
                                        Previous
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, (int)$current_page - 2); $i <= min((int)$total_pages, (int)$current_page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === (int)$current_page ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=<?php echo $current_status; ?>&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ((int)$current_page < (int)$total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=<?php echo $current_status; ?>&page=<?php echo (int)$current_page + 1; ?>">
                                        Next
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.notification-unread {
    border-left: 4px solid #dc3545;
    background-color: #f8f9fa;
}

.notification-icon {
    width: 40px;
    text-align: center;
}

.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.text-purple {
    color: #6f42c1 !important;
}

/* Compact notification layout */
.notification-item .card-body {
    padding: 0.75rem 1rem;
}

.notification-item .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.2;
}

.notification-item .btn-group-sm .btn i {
    font-size: 0.75rem;
}

/* Bulk actions styling */
#bulk-actions {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Checkbox styling */
.notification-checkbox {
    transform: scale(1.1);
}

/* Compact notification content */
.notification-item .notification-icon {
    width: 30px;
    text-align: center;
}

.notification-item h6 {
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.notification-item p {
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.notification-item small {
    font-size: 0.75rem;
}

/* Archive tab styling */
.nav-tabs .nav-link i {
    font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .notification-item .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .notification-item .btn-group-sm .btn i {
        font-size: 0.7rem;
    }

    #bulk-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }

    #bulk-actions .btn {
        margin-bottom: 0.25rem;
    }
}
</style>

<script>
function markAsRead(notificationId, showAlert = true) {
    return fetch('<?php echo BASE_URL; ?>/notification_center/markRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (showAlert) location.reload();
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (showAlert) alert('Failed to mark as read: ' + error.message);
        throw error;
    });
}

function markAllAsRead() {
    if (!confirm('Mark all notifications as read?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>/notification_center/markAllRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'type=<?php echo $current_type; ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to mark all as read: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to mark all as read');
    });
}

function archiveNotification(notificationId, showAlert = true) {
    if (showAlert && !confirm('Hide this notification?')) {
        return Promise.reject('User cancelled');
    }

    return fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (showAlert) location.reload();
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (showAlert) alert('Failed to hide notification: ' + error.message);
        throw error;
    });
}

function deleteNotification(notificationId, showAlert = true) {
    if (showAlert && !confirm('Permanently delete this notification? This cannot be undone.')) {
        return Promise.reject('User cancelled');
    }

    return fetch('<?php echo BASE_URL; ?>/notification_center/delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (showAlert) location.reload();
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (showAlert) alert('Failed to delete notification: ' + error.message);
        throw error;
    });
}

function unarchiveNotification(notificationId) {
    if (!confirm('Restore this notification to your active list?')) {
        return;
    }

    fetch('<?php echo BASE_URL; ?>/notification_center/unarchive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to restore notification: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to restore notification');
    });
}

function refreshNotifications() {
    location.reload();
}

// Bulk action functions
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.notification-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectAll = document.getElementById('select-all');

    if (checkboxes.length > 0) {
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }

    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.notification-checkbox');
    if (selectAll) {
        selectAll.checked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
        selectAll.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.notification-checkbox');
    const selectAll = document.getElementById('select-all');

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    if (selectAll) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    }

    updateBulkActions();
}

function getSelectedNotifications() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function bulkMarkAsRead() {
    const selected = getSelectedNotifications();
    if (selected.length === 0) {
        alert('Please select notifications to mark as read');
        return;
    }

    if (!confirm(`Mark ${selected.length} notification(s) as read?`)) {
        return;
    }

    // Process each notification
    Promise.all(selected.map(id => markAsRead(id, false)))
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Bulk mark as read failed:', error);
            alert('Some notifications could not be marked as read');
        });
}

function bulkArchive() {
    const selected = getSelectedNotifications();
    if (selected.length === 0) {
        alert('Please select notifications to hide');
        return;
    }

    if (!confirm(`Hide ${selected.length} notification(s)?`)) {
        return;
    }

    // Process each notification
    Promise.all(selected.map(id => archiveNotification(id, false)))
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Bulk archive failed:', error);
            alert('Some notifications could not be archived');
        });
}

function bulkDelete() {
    const selected = getSelectedNotifications();
    if (selected.length === 0) {
        alert('Please select notifications to delete');
        return;
    }

    if (!confirm(`Permanently delete ${selected.length} notification(s)? This cannot be undone.`)) {
        return;
    }

    // Process each notification
    Promise.all(selected.map(id => deleteNotification(id, false)))
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Bulk delete failed:', error);
            alert('Some notifications could not be deleted');
        });
}
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
