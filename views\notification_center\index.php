<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bell me-2"></i>Notification Center
                    <?php if ($counts['total_unread'] > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $counts['total_unread']; ?></span>
                    <?php endif; ?>
                </h1>
                
                <div class="btn-group" role="group">
                    <?php if ($counts['total_unread'] > 0): ?>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-1"></i>Mark All Read
                        </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshNotifications()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Filter Tabs -->
            <ul class="nav nav-tabs mb-4" id="notificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'all' ? 'active' : ''; ?>" 
                       href="<?php echo BASE_URL; ?>/notification_center?type=all&status=<?php echo $current_status; ?>">
                        All
                        <?php if ($counts['total_unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['total_unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'message' ? 'active' : ''; ?>" 
                       href="<?php echo BASE_URL; ?>/notification_center?type=message&status=<?php echo $current_status; ?>">
                        Messages
                        <?php if ($counts['message']['unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['message']['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'push' ? 'active' : ''; ?>" 
                       href="<?php echo BASE_URL; ?>/notification_center?type=push&status=<?php echo $current_status; ?>">
                        Push
                        <?php if ($counts['push']['unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['push']['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link <?php echo $current_type === 'system' ? 'active' : ''; ?>" 
                       href="<?php echo BASE_URL; ?>/notification_center?type=system&status=<?php echo $current_status; ?>">
                        System
                        <?php if ($counts['system']['unread'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?php echo $counts['system']['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            </ul>

            <!-- Status Filter -->
            <div class="mb-3">
                <div class="btn-group btn-group-sm" role="group">
                    <a href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=all" 
                       class="btn <?php echo $current_status === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        All
                    </a>
                    <a href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=unread" 
                       class="btn <?php echo $current_status === 'unread' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Unread
                    </a>
                    <a href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=read" 
                       class="btn <?php echo $current_status === 'read' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                        Read
                    </a>
                </div>
            </div>

            <!-- Notifications List -->
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No notifications found</h4>
                    <p class="text-muted">
                        <?php if ($current_status === 'unread'): ?>
                            You have no unread notifications.
                        <?php elseif ($current_type !== 'all'): ?>
                            No <?php echo $current_type; ?> notifications found.
                        <?php else: ?>
                            You don't have any notifications yet.
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="notification-list">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="card mb-3 notification-item <?php echo $notification->is_read ? '' : 'notification-unread'; ?>" 
                             data-notification-id="<?php echo $notification->id; ?>">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-start">
                                            <!-- Notification Icon -->
                                            <div class="notification-icon me-3">
                                                <?php
                                                $iconClass = 'fas fa-bell';
                                                $iconColor = 'text-primary';
                                                
                                                switch ($notification->notification_type) {
                                                    case 'message':
                                                        $iconClass = 'fas fa-envelope';
                                                        $iconColor = 'text-success';
                                                        break;
                                                    case 'push':
                                                        $iconClass = 'fas fa-mobile-alt';
                                                        $iconColor = 'text-info';
                                                        break;
                                                    case 'system':
                                                        $iconClass = 'fas fa-cog';
                                                        $iconColor = 'text-warning';
                                                        break;
                                                    case 'judging':
                                                        $iconClass = 'fas fa-gavel';
                                                        $iconColor = 'text-purple';
                                                        break;
                                                    case 'event':
                                                        $iconClass = 'fas fa-calendar';
                                                        $iconColor = 'text-primary';
                                                        break;
                                                }
                                                ?>
                                                <i class="<?php echo $iconClass; ?> <?php echo $iconColor; ?> fa-lg"></i>
                                            </div>
                                            
                                            <!-- Notification Content -->
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($notification->title); ?>
                                                    <?php if (!$notification->is_read): ?>
                                                        <span class="badge bg-danger ms-2">New</span>
                                                    <?php endif; ?>
                                                </h6>
                                                
                                                <?php if ($notification->from_user_name): ?>
                                                    <small class="text-muted d-block mb-1">
                                                        From: <?php echo htmlspecialchars($notification->from_user_name); ?>
                                                    </small>
                                                <?php endif; ?>
                                                
                                                <p class="mb-2 text-muted">
                                                    <?php 
                                                    $message = htmlspecialchars($notification->message);
                                                    echo strlen($message) > 150 ? substr($message, 0, 150) . '...' : $message;
                                                    ?>
                                                </p>
                                                
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo date('M j, Y g:i A', strtotime($notification->created_at)); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="col-md-4 text-end">
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <?php if ($notification->action_url): ?>
                                                <a href="<?php echo BASE_URL . $notification->action_url; ?>" 
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye me-1"></i>
                                                    <?php echo $notification->action_text ?: 'View'; ?>
                                                </a>
                                            <?php else: ?>
                                                <a href="<?php echo BASE_URL; ?>/notification_center/viewNotification/<?php echo $notification->id; ?>"
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if (!$notification->is_read): ?>
                                                <button type="button" class="btn btn-outline-success btn-sm" 
                                                        onclick="markAsRead(<?php echo $notification->id; ?>)">
                                                    <i class="fas fa-check me-1"></i>Mark Read
                                                </button>
                                            <?php endif; ?>
                                            
                                            <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                    onclick="archiveNotification(<?php echo $notification->id; ?>)">
                                                <i class="fas fa-archive me-1"></i>Archive
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Notifications pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($current_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=<?php echo $current_status; ?>&page=<?php echo $current_page - 1; ?>">
                                        Previous
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=<?php echo $current_status; ?>&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($current_page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?type=<?php echo $current_type; ?>&status=<?php echo $current_status; ?>&page=<?php echo $current_page + 1; ?>">
                                        Next
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.notification-unread {
    border-left: 4px solid #dc3545;
    background-color: #f8f9fa;
}

.notification-icon {
    width: 40px;
    text-align: center;
}

.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.text-purple {
    color: #6f42c1 !important;
}
</style>

<script>
function markAsRead(notificationId) {
    fetch('<?php echo BASE_URL; ?>/notification_center/markRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to mark as read: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to mark as read');
    });
}

function markAllAsRead() {
    if (!confirm('Mark all notifications as read?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>/notification_center/markAllRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'type=<?php echo $current_type; ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to mark all as read: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to mark all as read');
    });
}

function archiveNotification(notificationId) {
    if (!confirm('Archive this notification?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to archive: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to archive notification');
    });
}

function refreshNotifications() {
    location.reload();
}
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
