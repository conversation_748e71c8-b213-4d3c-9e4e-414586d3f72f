<?php
/**
 * Check user_messages Table Structure
 * 
 * This script checks the actual structure of the user_messages table
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load core classes
require_once 'core/Database.php';

echo "<h1>Check user_messages Table Structure</h1>";

try {
    $db = new Database();
    
    echo "<h2>1. Table Structure</h2>";
    
    // Show table structure
    try {
        $db->query("DESCRIBE user_messages");
        $columns = $db->resultSet();
        
        if (!empty($columns)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Field</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Null</th>";
            echo "<th style='padding: 8px;'>Key</th>";
            echo "<th style='padding: 8px;'>Default</th>";
            echo "<th style='padding: 8px;'>Extra</th>";
            echo "</tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td style='padding: 8px;'><strong>{$column->Field}</strong></td>";
                echo "<td style='padding: 8px;'>{$column->Type}</td>";
                echo "<td style='padding: 8px;'>{$column->Null}</td>";
                echo "<td style='padding: 8px;'>{$column->Key}</td>";
                echo "<td style='padding: 8px;'>{$column->Default}</td>";
                echo "<td style='padding: 8px;'>{$column->Extra}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ Could not get table structure</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error getting table structure: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>2. Sample Data</h2>";
    
    // Show sample data to understand the structure
    try {
        $db->query("SELECT * FROM user_messages ORDER BY created_at DESC LIMIT 5");
        $samples = $db->resultSet();
        
        if (!empty($samples)) {
            echo "<p>✅ Found " . count($samples) . " sample records</p>";
            
            // Show first record structure
            $firstRecord = $samples[0];
            echo "<h3>Sample Record Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Column</th>";
            echo "<th style='padding: 8px;'>Value</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "</tr>";
            
            foreach ($firstRecord as $column => $value) {
                $type = gettype($value);
                $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') : $value);
                
                echo "<tr>";
                echo "<td style='padding: 8px;'><strong>{$column}</strong></td>";
                echo "<td style='padding: 8px;'>{$displayValue}</td>";
                echo "<td style='padding: 8px;'>{$type}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<h3>All Sample Records:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            
            // Dynamic headers based on actual columns
            $headers = array_keys((array)$firstRecord);
            foreach ($headers as $header) {
                echo "<th style='padding: 8px;'>{$header}</th>";
            }
            echo "</tr>";
            
            foreach ($samples as $sample) {
                echo "<tr>";
                foreach ($headers as $header) {
                    $value = $sample->$header ?? 'NULL';
                    $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? htmlspecialchars(substr($value, 0, 30)) . (strlen($value) > 30 ? '...' : '') : $value);
                    echo "<td style='padding: 8px;'>{$displayValue}</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "<p>❌ No sample data found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error getting sample data: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Analysis</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Key Questions:</h4>";
    echo "<ul>";
    echo "<li><strong>Does the table have 'to_user_id' and 'from_user_id' columns?</strong></li>";
    echo "<li><strong>How are recipients identified?</strong></li>";
    echo "<li><strong>What columns link to users?</strong></li>";
    echo "<li><strong>How should we query messages for a specific user?</strong></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>4. Test Queries</h2>";
    
    // Test different query approaches based on what we find
    $userId = $_SESSION['user_id'] ?? 3;
    
    echo "<h3>Testing queries for User ID: {$userId}</h3>";
    
    // Try different column names that might exist
    $possibleColumns = ['to_user_id', 'user_id', 'recipient_id', 'recipient_user_id', 'target_user_id'];
    
    foreach ($possibleColumns as $column) {
        try {
            echo "<h4>Testing: SELECT * FROM user_messages WHERE {$column} = {$userId}</h4>";
            $db->query("SELECT COUNT(*) as count FROM user_messages WHERE {$column} = :user_id");
            $db->bind(':user_id', $userId);
            $result = $db->single();
            
            if ($result) {
                echo "<p>✅ Column '{$column}' exists - Found {$result->count} records for user {$userId}</p>";
                
                if ($result->count > 0) {
                    // Show sample records for this user
                    $db->query("SELECT * FROM user_messages WHERE {$column} = :user_id ORDER BY created_at DESC LIMIT 3");
                    $db->bind(':user_id', $userId);
                    $userRecords = $db->resultSet();
                    
                    echo "<p><strong>Sample records for user {$userId}:</strong></p>";
                    echo "<ul>";
                    foreach ($userRecords as $record) {
                        $subject = $record->subject ?? 'No subject';
                        $created = $record->created_at ?? 'No date';
                        echo "<li>ID: {$record->id}, Subject: " . htmlspecialchars(substr($subject, 0, 30)) . "..., Created: {$created}</li>";
                    }
                    echo "</ul>";
                }
            }
        } catch (Exception $e) {
            echo "<p>❌ Column '{$column}' does not exist or error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>5. Recommendations</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li><strong>Identify correct column names</strong> for querying user messages</li>";
    echo "<li><strong>Update NotificationCenterModel</strong> to use correct column names</li>";
    echo "<li><strong>Fix the sendMessage() method</strong> to work with actual table structure</li>";
    echo "<li><strong>Test message creation</strong> with correct structure</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
