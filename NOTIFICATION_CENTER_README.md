# Notification Center Implementation

This document describes the complete notification center system that has been implemented to solve the push notification routing issues and provide a comprehensive messaging system.

## Problem Solved

### Original Issues:
1. **Push notification routing errors** - Clicking push notifications caused `JudgeManagementController::index()` errors because notifications redirected to `/` without proper context
2. **No way to read notifications** - Users couldn't actually read their messages or notifications in the app
3. **No reply functionality** - Judge messages couldn't be replied to
4. **Poor user experience** - No centralized place to manage notifications

### Solution:
A complete notification center system that provides:
- Unified notification viewing and management
- Message threading and reply functionality  
- Proper push notification routing to `/notification_center`
- Real-time unread count updates in header
- Archive and read/unread management

## Files Created/Modified

### New Files:
- `controllers/NotificationCenterController.php` - Main controller for notification center
- `models/NotificationCenterModel.php` - Model for notification center operations
- `views/notification_center/index.php` - Main notification center page
- `views/notification_center/view.php` - Individual notification view page
- `database/migrations/add_notification_center.sql` - Database migration
- `public/js/notification-center.js` - JavaScript for real-time updates
- `sync_notification_center.php` - Migration/sync script
- `test_notification_center.php` - Testing script

### Modified Files:
- `views/includes/header.php` - Updated bell icon to use notification center
- `controllers/JudgeManagementController.php` - Updated to use notification center for messages
- `models/NotificationService.php` - Updated push notifications to redirect to notification center

## Database Schema

### New Tables:

#### `user_messages`
Stores direct messages between users with reply threading support.

#### `notification_center_items` 
Unified table for all notification types with action URLs and metadata.

### Modified Tables:
- `user_push_notifications` - Added `notification_center_id` column
- `user_toast_notifications` - Added `notification_center_id` column  
- `notification_queue` - Added `metadata` column

## Features

### 1. Unified Notification Center (`/notification_center`)
- View all notifications in one place
- Filter by type (messages, push, system, etc.)
- Filter by status (all, unread, read, archived)
- Pagination for large notification lists
- Real-time unread count updates

### 2. Message System
- Send messages between users (judge management integration)
- Reply functionality with conversation threading
- Mark messages as requiring replies
- Show context (related to specific car shows)

### 3. Header Integration
- Bell icon with unread count badge
- Links to notification center
- Real-time count updates via JavaScript

### 4. Push Notification Fix
- All push notifications now redirect to `/notification_center` instead of `/`
- Eliminates routing errors when clicking notifications
- Creates notification center items for all push notifications

### 5. Smart Action URLs
- Each notification can have custom action URLs
- Context-aware actions (Reply for messages, View for events, etc.)
- Fallback to notification center view

## Usage

### For Users:
1. **Access**: Click bell icon in header or go to `/notification_center`
2. **View**: Click any notification to see full details
3. **Reply**: Use reply form for messages that require responses
4. **Manage**: Mark as read, archive, or filter notifications

### For Developers:

#### Sending Messages:
```php
$notificationCenterModel = new NotificationCenterModel();
$messageId = $notificationCenterModel->sendMessage(
    $fromUserId,
    $toUserId, 
    'Subject',
    'Message content',
    $showId, // optional
    true // requires reply
);
```

#### Creating Notifications:
```php
$notificationId = $notificationCenterModel->createNotificationItem(
    $userId,
    'system', // type
    'Title',
    'Message',
    'source_table', // optional
    $sourceId, // optional
    '/action/url', // optional
    'Action Text', // optional
    ['key' => 'value'] // metadata
);
```

#### Getting Counts:
```php
$counts = $notificationCenterModel->getNotificationCounts($userId);
echo $counts['total_unread']; // Total unread count
echo $counts['message']['unread']; // Unread messages
```

## Installation

### 1. Run Database Migration:
```bash
# Via web interface:
http://yoursite.com/sync_notification_center.php

# Or run SQL manually:
mysql -u username -p database < database/migrations/add_notification_center.sql
```

### 2. Test Installation:
```bash
# Via web interface:
http://yoursite.com/test_notification_center.php
```

### 3. Sync Existing Notifications:
The sync script automatically migrates existing push/toast notifications to the notification center.

## API Endpoints

### AJAX Endpoints:
- `POST /notification_center/markRead` - Mark notification as read
- `POST /notification_center/markAllRead` - Mark all notifications as read
- `POST /notification_center/archive` - Archive notification
- `GET /notification_center/getUnreadCount` - Get unread count for header badge
- `POST /notification_center/reply` - Send reply to message

### Page Endpoints:
- `GET /notification_center` - Main notification center page
- `GET /notification_center/viewNotification/{id}` - View specific notification

## JavaScript Integration

The notification center includes real-time JavaScript functionality:

```javascript
// Global instance available
window.notificationCenter

// Manual updates
notificationCenter.updateNotificationCount();

// Mark as read
notificationCenter.markAsRead(notificationId);

// Events
window.addEventListener('notificationCountUpdated', (event) => {
    console.log('New count:', event.detail.totalUnread);
});
```

## Security

- All database queries use prepared statements
- User authentication required for all operations
- Users can only access their own notifications
- CSRF protection on all forms
- XSS protection with proper escaping

## Performance

- Pagination for large notification lists
- Efficient database indexes
- Real-time updates only when page is visible
- Minimal JavaScript footprint
- Lazy loading of notification details

## Troubleshooting

### Common Issues:

1. **"Notification not found" errors**
   - Check user permissions
   - Verify notification belongs to current user

2. **Push notifications still redirect to `/`**
   - Clear browser cache
   - Check service worker registration
   - Verify NotificationService updates

3. **Unread counts not updating**
   - Check JavaScript console for errors
   - Verify AJAX endpoints are accessible
   - Check user session status

### Debug Mode:
Set `DEBUG_MODE = true` in config to enable detailed logging.

## Future Enhancements

Potential improvements for future versions:
- Real-time WebSocket updates
- Email digest notifications
- Notification preferences per type
- Bulk operations (mark multiple as read)
- Search functionality
- Export/backup capabilities

## Support

For issues or questions about the notification center:
1. Check the test script: `/test_notification_center.php`
2. Review browser console for JavaScript errors
3. Check server error logs for PHP errors
4. Verify database migration completed successfully
