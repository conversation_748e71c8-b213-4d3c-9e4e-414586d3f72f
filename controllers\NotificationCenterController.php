<?php
/**
 * Notification Center Controller
 * 
 * This controller handles the notification center functionality including
 * viewing notifications, messages, and handling replies.
 */
class NotificationCenterController extends Controller {
    private $auth;
    private $db;
    private $notificationCenterModel;
    private $userModel;
    private $showModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Call parent constructor first
        parent::__construct();

        // Initialize core dependencies
        $this->auth = new Auth();
        $this->db = new Database();
        
        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Initialize models
        $this->notificationCenterModel = $this->model('NotificationCenterModel');
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Default index method - shows notification center
     */
    public function index() {
        $this->center();
    }
    
    /**
     * Main notification center page
     */
    public function center() {
        $userId = $this->auth->getCurrentUserId();
        
        // Get filter parameters
        $type = $_GET['type'] ?? 'all';
        $status = $_GET['status'] ?? 'all';
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // Get notifications
        $notifications = $this->notificationCenterModel->getUserNotifications(
            $userId, 
            $type, 
            $status, 
            $limit, 
            $offset
        );
        
        // Get counts for badges
        $counts = $this->notificationCenterModel->getNotificationCounts($userId);
        
        // Get total count for pagination
        $totalCount = (int)$this->notificationCenterModel->getTotalNotificationCount($userId, $type, $status);
        $totalPages = (int)ceil($totalCount / $limit);
        
        $data = [
            'title' => 'Notification Center',
            'notifications' => $notifications,
            'counts' => $counts,
            'current_type' => $type,
            'current_status' => $status,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount
        ];
        
        parent::view('notification_center/index', $data);
    }
    
    /**
     * View a specific notification
     */
    public function viewNotification($notificationId) {
        $userId = $this->auth->getCurrentUserId();
        $notificationId = (int)$notificationId;
        
        if ($notificationId <= 0) {
            $this->redirect('notification_center');
            return;
        }
        
        // Get notification
        $notification = $this->notificationCenterModel->getNotificationById($notificationId, $userId);
        
        if (!$notification) {
            $this->setFlashMessage('error', 'Notification not found', 'danger');
            $this->redirect('notification_center');
            return;
        }
        
        // Mark as read
        $this->notificationCenterModel->markAsRead($notificationId, $userId);
        
        // If it's a message, get the conversation thread
        $conversation = null;
        if ($notification->notification_type === 'message' && $notification->source_id) {
            $conversation = $this->notificationCenterModel->getMessageThread($notification->source_id);
        }
        
        $data = [
            'title' => 'View Notification',
            'notification' => $notification,
            'conversation' => $conversation
        ];
        
        parent::view('notification_center/view', $data);
    }
    
    /**
     * Send a reply to a message
     */
    public function reply() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('notification_center');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $parentMessageId = (int)($_POST['parent_message_id'] ?? 0);
        $message = trim($_POST['message'] ?? '');
        
        if ($parentMessageId <= 0 || empty($message)) {
            $this->setFlashMessage('error', 'Please provide a valid message', 'danger');
            $this->redirect('notification_center');
            return;
        }
        
        try {
            // Get the original message
            $originalMessage = $this->notificationCenterModel->getMessageById($parentMessageId);
            
            if (!$originalMessage || $originalMessage->to_user_id != $userId) {
                $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
                $this->redirect('notification_center');
                return;
            }
            
            // Send the reply
            $replyId = $this->notificationCenterModel->sendReply(
                $userId,
                $originalMessage->from_user_id,
                $originalMessage->subject,
                $message,
                $parentMessageId,
                $originalMessage->show_id
            );
            
            if ($replyId) {
                $this->setFlashMessage('success', 'Reply sent successfully', 'success');
            } else {
                $this->setFlashMessage('error', 'Failed to send reply', 'danger');
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::reply - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to send reply', 'danger');
        }
        
        $this->redirect('notification_center');
    }
    
    /**
     * Mark notification as read (AJAX)
     */
    public function markRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $notificationId = (int)($_POST['notification_id'] ?? 0);
        
        if ($notificationId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid notification ID']);
            return;
        }
        
        try {
            $success = $this->notificationCenterModel->markAsRead($notificationId, $userId);
            
            if ($success) {
                // Get updated counts
                $counts = $this->notificationCenterModel->getNotificationCounts($userId);
                $this->jsonResponse([
                    'success' => true,
                    'counts' => $counts
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark as read']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::markRead - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
    
    /**
     * Mark all notifications as read (AJAX)
     */
    public function markAllRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $type = $_POST['type'] ?? 'all';
        
        try {
            $success = $this->notificationCenterModel->markAllAsRead($userId, $type);
            
            if ($success) {
                // Get updated counts
                $counts = $this->notificationCenterModel->getNotificationCounts($userId);
                $this->jsonResponse([
                    'success' => true,
                    'counts' => $counts
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark all as read']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::markAllRead - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
    
    /**
     * Get unread notification count for header badge (AJAX)
     */
    public function getUnreadCount() {
        $userId = $this->auth->getCurrentUserId();
        
        try {
            $counts = $this->notificationCenterModel->getNotificationCounts($userId);
            $this->jsonResponse([
                'success' => true,
                'total_unread' => $counts['total_unread'],
                'counts' => $counts
            ]);
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::getUnreadCount - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'total_unread' => 0]);
        }
    }
    
    /**
     * Archive a notification
     */
    public function archive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $notificationId = (int)($_POST['notification_id'] ?? 0);
        
        if ($notificationId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid notification ID']);
            return;
        }
        
        try {
            $success = $this->notificationCenterModel->archiveNotification($notificationId, $userId);
            
            if ($success) {
                $this->jsonResponse(['success' => true]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to archive notification']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::archive - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
}
