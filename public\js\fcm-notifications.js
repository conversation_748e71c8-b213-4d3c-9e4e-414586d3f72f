/**
 * FCM Notifications JavaScript v1.0.0
 * 
 * This file handles Firebase Cloud Messaging (FCM) for push notifications
 * using the Firebase Web SDK v9+ modular approach.
 * 
 * Location: /public/js/fcm-notifications.js
 * Dependencies: Firebase Web SDK
 */

// Import Firebase modules (will be loaded from CDN)
let messaging = null;
let isFirebaseInitialized = false;

/**
 * FCM Notification Manager Class
 */
class FCMNotificationManager {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.debugMode = localStorage.getItem('notification_debug') === 'true' ||
                         document.body.dataset.debugMode === 'true' ||
                         window.location.search.includes('debug=1');
        this.fcmToken = null;
        this.isSupported = this.checkSupport();
        
        if (this.debugMode) {
            console.log('[FCM] FCM Notification Manager initialized');
            console.log('[FCM] Is supported:', this.isSupported);
        }
        
        if (this.isSupported) {
            this.initializeFirebase();
        }
    }
    
    /**
     * Check if FCM is supported in this browser
     */
    checkSupport() {
        return 'serviceWorker' in navigator && 
               'PushManager' in window && 
               'Notification' in window &&
               !this.isIOS();
    }
    
    /**
     * Check if device is iOS (FCM not supported on iOS Safari)
     */
    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }
    
    /**
     * Initialize Firebase and FCM
     */
    async initializeFirebase() {
        try {
            // Wait for Firebase SDK to load
            if (typeof firebase === 'undefined') {
                console.log('[FCM] Waiting for Firebase SDK to load...');
                await this.waitForFirebase();
            }
            
            // Firebase configuration for your project
            const firebaseConfig = {
                apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
                authDomain: "rowaneliterides.firebaseapp.com",
                projectId: "rowaneliterides",
                storageBucket: "rowaneliterides.firebasestorage.app",
                messagingSenderId: "310533125467",
                appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
            };
            
            // Initialize Firebase
            if (!firebase.apps.length) {
                firebase.initializeApp(firebaseConfig);
            }
            
            // Initialize FCM
            messaging = firebase.messaging();
            isFirebaseInitialized = true;
            
            // Set up service worker for FCM
            await this.setupServiceWorker();
            
            if (this.debugMode) {
                console.log('[FCM] Firebase initialized successfully');
            }
            
        } catch (error) {
            console.error('[FCM] Firebase initialization failed:', error);
        }
    }
    
    /**
     * Wait for Firebase SDK to load
     */
    waitForFirebase() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 5 seconds
            
            const checkFirebase = () => {
                attempts++;
                if (typeof firebase !== 'undefined') {
                    resolve();
                } else if (attempts >= maxAttempts) {
                    reject(new Error('Firebase SDK failed to load'));
                } else {
                    setTimeout(checkFirebase, 100);
                }
            };
            
            checkFirebase();
        });
    }
    
    /**
     * Setup service worker for FCM
     */
    async setupServiceWorker() {
        try {
            // Register Firebase messaging service worker
            if ('serviceWorker' in navigator) {
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                
                if (this.debugMode) {
                    console.log('[FCM] Firebase messaging service worker registered:', registration.scope);
                }
                
                // Wait for service worker to be ready
                await navigator.serviceWorker.ready;
                
                return registration;
            }
        } catch (error) {
            console.error('[FCM] Service worker setup failed:', error);
            return null;
        }
    }
    
    /**
     * Unregister old FCM service worker if it exists
     */
    async unregisterOldFCMWorker() {
        try {
            if ('serviceWorker' in navigator) {
                const registrations = await navigator.serviceWorker.getRegistrations();
                
                for (const registration of registrations) {
                    if (registration.scope.includes('firebase-messaging-sw') || 
                        (registration.active && registration.active.scriptURL.includes('firebase-messaging-sw'))) {
                        console.log('[FCM] Unregistering old FCM service worker:', registration.scope);
                        await registration.unregister();
                        console.log('[FCM] Old FCM service worker unregistered successfully');
                    }
                }
            }
        } catch (error) {
            console.error('[FCM] Error unregistering old FCM service worker:', error);
        }
    }
    
    /**
     * Initialize FCM and automatically request permission (for auto-setup)
     */
    async init() {
        if (!this.isSupported) {
            console.log('[FCM] FCM not supported on this device/browser');
            return false;
        }
        
        // Wait for Firebase to initialize
        if (!isFirebaseInitialized) {
            console.log('[FCM] Waiting for Firebase to initialize...');
            await this.waitForFirebase();
        }
        
        // Check current permission
        if (Notification.permission === 'granted') {
            console.log('[FCM] Permission already granted, generating token...');
            return await this.requestPermissionAndGetToken();
        } else if (Notification.permission === 'default') {
            console.log('[FCM] Permission not requested yet');
            return false; // Don't auto-request, let user trigger it
        } else {
            console.log('[FCM] Permission denied');
            return false;
        }
    }
    
    /**
     * Request notification permission and get FCM token
     */
    async requestPermissionAndGetToken() {
        if (!this.isSupported) {
            if (this.isIOS()) {
                this.showAlert('info', 'Push notifications are not supported on iOS Safari. Please use Chrome or Firefox for the best experience.');
            } else {
                this.showAlert('error', 'Push notifications are not supported in this browser.');
            }
            return null;
        }
        
        if (!isFirebaseInitialized) {
            this.showAlert('error', 'Firebase is not initialized. Please refresh the page and try again.');
            return null;
        }
        
        try {
            // Request notification permission
            const permission = await Notification.requestPermission();
            
            if (permission !== 'granted') {
                this.showAlert('warning', 'Notification permission denied. You won\'t receive push notifications.');
                return null;
            }
            
            // Ensure service worker is ready before getting token
            const registration = await navigator.serviceWorker.ready;
            if (!registration || !registration.active) {
                throw new Error('No active service worker found');
            }
            
            if (this.debugMode) {
                console.log('[FCM] Service worker ready for token generation:', registration.scope);
            }
            
            // Get FCM registration token
            const token = await messaging.getToken({
                vapidKey: await this.getVAPIDKey(),
                serviceWorkerRegistration: registration
            });
            
            if (token) {
                this.fcmToken = token;
                
                if (this.debugMode) {
                    console.log('[FCM] Registration token obtained:', token.substring(0, 20) + '...');
                }
                
                // Send token to server
                await this.sendTokenToServer(token);
                
                this.showAlert('success', 'Push notifications enabled successfully!');
                return token;
            } else {
                throw new Error('No registration token available');
            }
            
        } catch (error) {
            console.error('[FCM] Error getting permission/token:', error);
            this.showAlert('error', 'Failed to enable push notifications: ' + error.message);
            return null;
        }
    }
    
    /**
     * Get VAPID key from server
     */
    async getVAPIDKey() {
        try {
            const response = await fetch(`${this.baseUrl}/api/pwa/vapid-key`);
            const data = await response.json();
            
            if (data.success && data.publicKey) {
                return data.publicKey;
            } else {
                throw new Error('Failed to get VAPID key');
            }
        } catch (error) {
            console.error('[FCM] Failed to get VAPID key:', error);
            // Return a default VAPID key if server fails
            return 'BDefault-VAPID-Key-Here'; // You'll need to set this
        }
    }
    
    /**
     * Send FCM token to server
     */
    async sendTokenToServer(token) {
        try {
            const response = await fetch(`${this.baseUrl}/api/pwa/fcm-subscribe`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    fcm_token: token,
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to save FCM token');
            }
            
            if (this.debugMode) {
                console.log('[FCM] Token sent to server successfully');
            }
            
        } catch (error) {
            console.error('[FCM] Failed to send token to server:', error);
            throw error;
        }
    }
    
    /**
     * Setup foreground message handling
     */
    setupForegroundMessageHandling() {
        if (!isFirebaseInitialized) return;
        
        messaging.onMessage((payload) => {
            if (this.debugMode) {
                console.log('[FCM] Foreground message received:', payload);
            }
            
            // Show notification when app is in foreground
            this.showForegroundNotification(payload);
        });
    }
    
    /**
     * Show notification when app is in foreground
     */
    showForegroundNotification(payload) {
        const { notification, data } = payload;
        
        if (notification) {
            // Create a visual notification
            this.showToastNotification(
                notification.title || 'New Notification',
                notification.body || 'You have a new notification',
                notification.icon || '/public/images/icons/icon-192x192.png'
            );
        }
    }
    
    /**
     * Show toast notification
     */
    showToastNotification(title, body, icon) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'fcm-toast-notification';
        toast.innerHTML = `
            <div class="fcm-toast-content">
                <img src="${icon}" alt="Icon" class="fcm-toast-icon">
                <div class="fcm-toast-text">
                    <div class="fcm-toast-title">${title}</div>
                    <div class="fcm-toast-body">${body}</div>
                </div>
                <button class="fcm-toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        // Add styles
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            animation: slideIn 0.3s ease-out;
        `;
        
        // Add to page
        document.body.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
    
    /**
     * Show alert message
     */
    showAlert(type, message) {
        // Use existing notification system if available
        if (window.NotificationManager && window.NotificationManager.showAlert) {
            window.NotificationManager.showAlert(type, message);
        } else {
            // Fallback to console
            console.log(`[FCM] ${type.toUpperCase()}: ${message}`);
        }
    }
    
    /**
     * Get current FCM token
     */
    getCurrentToken() {
        return this.fcmToken;
    }
    
    /**
     * Check if notifications are enabled
     */
    isNotificationEnabled() {
        return Notification.permission === 'granted' && this.fcmToken !== null;
    }
}

// CSS for toast notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .fcm-toast-content {
        display: flex;
        align-items: center;
        padding: 12px;
    }
    
    .fcm-toast-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        border-radius: 4px;
    }
    
    .fcm-toast-text {
        flex: 1;
    }
    
    .fcm-toast-title {
        font-weight: bold;
        margin-bottom: 4px;
        color: #333;
    }
    
    .fcm-toast-body {
        color: #666;
        font-size: 14px;
    }
    
    .fcm-toast-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #999;
        margin-left: 8px;
    }
    
    .fcm-toast-close:hover {
        color: #333;
    }
`;
document.head.appendChild(style);

// Initialize FCM when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if user is logged in
    if (window.PWA_CONFIG && window.PWA_CONFIG.userId) {
        window.fcmManager = new FCMNotificationManager();
        
        // Setup foreground message handling and auto-init
        setTimeout(async () => {
            if (window.fcmManager) {
                window.fcmManager.setupForegroundMessageHandling();
                
                // Auto-initialize FCM if permission already granted
                await window.fcmManager.init();
            }
        }, 2000); // Give more time for Firebase to load
    }
});

// Make FCM manager globally available
window.FCMNotificationManager = FCMNotificationManager;