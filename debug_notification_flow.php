<?php
/**
 * Debug Notification Flow
 * 
 * This script debugs the complete flow from message creation to notification center display
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Debug Notification Flow</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to debug notification flow.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    $db = new Database();
    
    echo "<p><strong>Debugging for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. Current Data State</h2>";
    
    // Check user_messages for this user
    try {
        $db->query("SELECT * FROM user_messages WHERE to_user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $userMessages = $db->resultSet();
        
        echo "<h3>user_messages table (to_user_id = {$userId}):</h3>";
        if (!empty($userMessages)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>From</th>";
            echo "<th style='padding: 8px;'>Subject</th>";
            echo "<th style='padding: 8px;'>Allows Reply</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";
            
            foreach ($userMessages as $message) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$message->id}</td>";
                echo "<td style='padding: 8px;'>{$message->from_user_id}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($message->subject, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>" . ($message->allows_reply ? 'Yes' : 'No') . "</td>";
                echo "<td style='padding: 8px;'>{$message->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No messages found in user_messages table</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking user_messages: " . $e->getMessage() . "</p>";
    }
    
    // Check notification_center_items for this user
    try {
        $db->query("SELECT * FROM notification_center_items WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $notificationItems = $db->resultSet();
        
        echo "<h3>notification_center_items table (user_id = {$userId}):</h3>";
        if (!empty($notificationItems)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Source</th>";
            echo "<th style='padding: 8px;'>Title</th>";
            echo "<th style='padding: 8px;'>Read</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";
            
            foreach ($notificationItems as $item) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$item->id}</td>";
                echo "<td style='padding: 8px;'>{$item->notification_type}</td>";
                echo "<td style='padding: 8px;'>{$item->source_table}:{$item->source_id}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($item->title, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>" . ($item->is_read ? 'Yes' : 'No') . "</td>";
                echo "<td style='padding: 8px;'>{$item->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ No items found in notification_center_items table</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking notification_center_items: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>2. Test Notification Center Model</h2>";
    
    // Test the notification center model methods
    try {
        $notifications = $notificationCenterModel->getUserNotifications($userId, 'all', 'all', 10, 0);
        
        echo "<h3>NotificationCenterModel::getUserNotifications() result:</h3>";
        if (!empty($notifications)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Title</th>";
            echo "<th style='padding: 8px;'>From User</th>";
            echo "<th style='padding: 8px;'>Created</th>";
            echo "</tr>";
            
            foreach ($notifications as $notification) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$notification->id}</td>";
                echo "<td style='padding: 8px;'>{$notification->notification_type}</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($notification->title, 0, 30)) . "...</td>";
                echo "<td style='padding: 8px;'>" . ($notification->from_user_name ?? 'N/A') . "</td>";
                echo "<td style='padding: 8px;'>{$notification->created_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>❌ getUserNotifications() returned empty array</p>";
        }
        
        // Test notification counts
        $counts = $notificationCenterModel->getNotificationCounts($userId);
        echo "<h3>Notification Counts:</h3>";
        echo "<ul>";
        echo "<li><strong>Total Unread:</strong> {$counts['total_unread']}</li>";
        echo "<li><strong>Message Unread:</strong> {$counts['message']['unread']}</li>";
        echo "<li><strong>Push Unread:</strong> {$counts['push']['unread']}</li>";
        echo "<li><strong>Toast Unread:</strong> {$counts['toast']['unread']}</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error testing NotificationCenterModel: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>3. Test Message Creation</h2>";
    
    if ($_POST['action'] ?? '' === 'test_full_flow') {
        try {
            echo "<h3>Creating Test Message...</h3>";
            
            $subject = "Flow Test Message - " . date('Y-m-d H:i:s');
            $message = "This is a test message to debug the complete flow.\n\n[reply]";
            
            echo "<p><strong>Calling sendMessage()...</strong></p>";
            
            $messageId = $notificationCenterModel->sendMessage(
                $userId,     // from
                $userId,     // to (self)
                $subject,    // subject
                $message,    // message
                null,        // show_id
                false        // requires_reply
            );
            
            if ($messageId) {
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ Message Created Successfully!</h4>";
                echo "<p><strong>Message ID:</strong> {$messageId}</p>";
                echo "</div>";
                
                // Check what was created
                echo "<h4>Checking what was created:</h4>";
                
                // Check user_messages
                $db->query("SELECT * FROM user_messages WHERE id = :message_id");
                $db->bind(':message_id', $messageId);
                $createdMessage = $db->single();
                
                if ($createdMessage) {
                    echo "<p>✅ Message found in user_messages table</p>";
                    echo "<ul>";
                    echo "<li><strong>ID:</strong> {$createdMessage->id}</li>";
                    echo "<li><strong>From:</strong> {$createdMessage->from_user_id}</li>";
                    echo "<li><strong>To:</strong> {$createdMessage->to_user_id}</li>";
                    echo "<li><strong>Subject:</strong> " . htmlspecialchars($createdMessage->subject) . "</li>";
                    echo "</ul>";
                } else {
                    echo "<p>❌ Message NOT found in user_messages table</p>";
                }
                
                // Check notification_center_items
                $db->query("SELECT * FROM notification_center_items WHERE source_table = 'user_messages' AND source_id = :message_id");
                $db->bind(':message_id', $messageId);
                $notificationItem = $db->single();
                
                if ($notificationItem) {
                    echo "<p>✅ Notification center item created</p>";
                    echo "<ul>";
                    echo "<li><strong>ID:</strong> {$notificationItem->id}</li>";
                    echo "<li><strong>User ID:</strong> {$notificationItem->user_id}</li>";
                    echo "<li><strong>Type:</strong> {$notificationItem->notification_type}</li>";
                    echo "<li><strong>Title:</strong> " . htmlspecialchars($notificationItem->title) . "</li>";
                    echo "</ul>";
                    
                    $viewUrl = BASE_URL . "/notification_center/viewNotification/" . $notificationItem->id;
                    echo "<p><a href='{$viewUrl}' target='_blank' class='btn btn-primary'>View in Notification Center</a></p>";
                } else {
                    echo "<p>❌ Notification center item NOT created</p>";
                }
                
                // Test if notification center can find it
                echo "<h4>Testing if NotificationCenterModel can find it:</h4>";
                $foundNotifications = $notificationCenterModel->getUserNotifications($userId, 'message', 'all', 10, 0);
                $found = false;
                foreach ($foundNotifications as $notif) {
                    if ($notif->source_id == $messageId) {
                        $found = true;
                        break;
                    }
                }
                
                if ($found) {
                    echo "<p>✅ NotificationCenterModel can find the new message</p>";
                } else {
                    echo "<p>❌ NotificationCenterModel CANNOT find the new message</p>";
                }
                
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>❌ Failed to Create Message</h4>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ Error in Full Flow Test</h4>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "<p><strong>Stack trace:</strong></p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
            echo "</div>";
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Test Complete Message Flow</h4>";
    echo "<p>This will test the complete flow from sendMessage() to notification center display:</p>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='test_full_flow'>";
    echo "<button type='submit' class='btn btn-primary'>Test Complete Flow</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<h2>4. Quick Actions</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Debug tools:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/sync_notification_center.php' target='_blank'>Run Sync Script</a> (re-sync existing notifications)</li>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Notification Center</a> (see what's displayed)</li>";
    echo "<li><a href='" . BASE_URL . "/simple_message_test.php' target='_blank'>Simple Message Test</a> (bypass sendMessage)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
