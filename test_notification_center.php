<?php
/**
 * Test Notification Center
 * 
 * This script tests the notification center functionality
 */

// Include the configuration
require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';
require_once 'models/UserModel.php';

echo "<h1>Notification Center Test</h1>";

try {
    $db = new Database();
    $notificationCenterModel = new NotificationCenterModel();
    $userModel = new UserModel();
    
    echo "<h2>1. Testing Database Connection</h2>";
    
    // Test database connection
    $db->query("SELECT 1 as test");
    $result = $db->single();
    if ($result && $result->test == 1) {
        echo "<p>✅ Database connection successful</p>";
    } else {
        throw new Exception("Database connection failed");
    }
    
    echo "<h2>2. Testing NotificationCenterModel</h2>";
    
    // Get a test user
    $db->query("SELECT id FROM users WHERE status = 'active' LIMIT 1");
    $testUser = $db->single();
    
    if (!$testUser) {
        echo "<p>❌ No active users found for testing</p>";
        exit;
    }
    
    $testUserId = $testUser->id;
    echo "<p>Using test user ID: {$testUserId}</p>";
    
    // Test creating a notification center item
    echo "<h3>Creating Test Notification</h3>";
    $notificationId = $notificationCenterModel->createNotificationItem(
        $testUserId,
        'system',
        'Test Notification',
        'This is a test notification for the notification center system.',
        null,
        null,
        '/notification_center',
        'View',
        ['test' => true]
    );
    
    if ($notificationId) {
        echo "<p>✅ Test notification created successfully (ID: {$notificationId})</p>";
    } else {
        echo "<p>❌ Failed to create test notification</p>";
    }
    
    // Test getting notification counts
    echo "<h3>Testing Notification Counts</h3>";
    $counts = $notificationCenterModel->getNotificationCounts($testUserId);
    echo "<p>Notification counts:</p>";
    echo "<ul>";
    echo "<li>Total unread: {$counts['total_unread']}</li>";
    echo "<li>Total count: {$counts['total_count']}</li>";
    echo "<li>System unread: {$counts['system']['unread']}</li>";
    echo "<li>Message unread: {$counts['message']['unread']}</li>";
    echo "</ul>";
    
    // Test getting user notifications
    echo "<h3>Testing Get User Notifications</h3>";
    $notifications = $notificationCenterModel->getUserNotifications($testUserId, 'all', 'all', 5, 0);
    echo "<p>Found " . count($notifications) . " notifications</p>";
    
    if (!empty($notifications)) {
        echo "<p>Latest notification:</p>";
        $latest = $notifications[0];
        echo "<ul>";
        echo "<li>ID: {$latest->id}</li>";
        echo "<li>Type: {$latest->notification_type}</li>";
        echo "<li>Title: {$latest->title}</li>";
        echo "<li>Read: " . ($latest->is_read ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
    }
    
    // Test sending a message
    echo "<h3>Testing Send Message</h3>";
    
    // Get another user to send message to
    $db->query("SELECT id FROM users WHERE status = 'active' AND id != :test_user_id LIMIT 1");
    $db->bind(':test_user_id', $testUserId);
    $otherUser = $db->single();
    
    if ($otherUser) {
        $messageId = $notificationCenterModel->sendMessage(
            $testUserId,
            $otherUser->id,
            'Test Message',
            'This is a test message from the notification center system.',
            null,
            true
        );
        
        if ($messageId) {
            echo "<p>✅ Test message sent successfully (ID: {$messageId})</p>";
        } else {
            echo "<p>❌ Failed to send test message</p>";
        }
    } else {
        echo "<p>⚠️ No other users found to test messaging</p>";
    }
    
    echo "<h2>3. Testing Database Tables</h2>";
    
    // Check if tables exist
    $tables = [
        'notification_center_items',
        'user_messages'
    ];
    
    foreach ($tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM {$table}");
            $result = $db->single();
            echo "<p>✅ Table '{$table}' exists with {$result->count} records</p>";
        } catch (Exception $e) {
            echo "<p>❌ Table '{$table}' error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. Testing Controller Access</h2>";
    
    // Test if the controller file exists
    $controllerFile = 'controllers/NotificationCenterController.php';
    if (file_exists($controllerFile)) {
        echo "<p>✅ NotificationCenterController file exists</p>";
        
        // Test if we can include it
        try {
            require_once $controllerFile;
            echo "<p>✅ NotificationCenterController can be loaded</p>";
        } catch (Exception $e) {
            echo "<p>❌ Error loading NotificationCenterController: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ NotificationCenterController file not found</p>";
    }
    
    echo "<h2>5. Testing View Files</h2>";
    
    $viewFiles = [
        'views/notification_center/index.php',
        'views/notification_center/view.php'
    ];
    
    foreach ($viewFiles as $viewFile) {
        if (file_exists($viewFile)) {
            echo "<p>✅ View file '{$viewFile}' exists</p>";
        } else {
            echo "<p>❌ View file '{$viewFile}' not found</p>";
        }
    }
    
    echo "<h2>✅ Notification Center Test Complete!</h2>";
    echo "<p>You can now test the notification center at: <a href='" . BASE_URL . "/notification_center'>" . BASE_URL . "/notification_center</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
