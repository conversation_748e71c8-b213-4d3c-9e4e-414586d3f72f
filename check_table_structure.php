<?php
/**
 * Check Table Structure
 * 
 * This script checks the structure of notification_center_items table
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load core classes
require_once 'core/Database.php';

echo "<h1>Check Table Structure</h1>";

try {
    $db = new Database();
    
    echo "<h2>notification_center_items Table Structure</h2>";
    
    // Show table structure
    $db->query("DESCRIBE notification_center_items");
    $columns = $db->resultSet();
    
    if (!empty($columns)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Field</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Null</th>";
        echo "<th style='padding: 8px;'>Key</th>";
        echo "<th style='padding: 8px;'>Default</th>";
        echo "<th style='padding: 8px;'>Extra</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td style='padding: 8px;'><strong>{$column->Field}</strong></td>";
            echo "<td style='padding: 8px;'>{$column->Type}</td>";
            echo "<td style='padding: 8px;'>{$column->Null}</td>";
            echo "<td style='padding: 8px;'>{$column->Key}</td>";
            echo "<td style='padding: 8px;'>{$column->Default}</td>";
            echo "<td style='padding: 8px;'>{$column->Extra}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ Could not get table structure</p>";
    }
    
    echo "<h2>Test Insert</h2>";
    
    // Test a simple insert
    try {
        $testSql = "INSERT INTO notification_center_items 
                    (user_id, notification_type, source_table, source_id, title, message, action_url, action_text, metadata, created_at) 
                    VALUES (3, 'message', 'user_messages', 999, 'Test Title', 'Test Message', '/test', 'View', NULL, NOW())";
        
        echo "<p><strong>Test SQL:</strong></p>";
        echo "<pre>" . htmlspecialchars($testSql) . "</pre>";
        
        $db->query($testSql);
        $result = $db->execute();
        
        if ($result) {
            $insertId = $db->lastInsertId();
            echo "<p>✅ Test insert successful! Insert ID: {$insertId}</p>";
            
            // Clean up test record
            $db->query("DELETE FROM notification_center_items WHERE id = :id");
            $db->bind(':id', $insertId);
            $db->execute();
            echo "<p>✅ Test record cleaned up</p>";
        } else {
            echo "<p>❌ Test insert failed</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Test insert error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Sample Data</h2>";
    
    // Show sample data
    $db->query("SELECT * FROM notification_center_items ORDER BY created_at DESC LIMIT 3");
    $samples = $db->resultSet();
    
    if (!empty($samples)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>User ID</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Source</th>";
        echo "<th style='padding: 8px;'>Title</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$sample->id}</td>";
            echo "<td style='padding: 8px;'>{$sample->user_id}</td>";
            echo "<td style='padding: 8px;'>{$sample->notification_type}</td>";
            echo "<td style='padding: 8px;'>{$sample->source_table}:{$sample->source_id}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($sample->title, 0, 30)) . "...</td>";
            echo "<td style='padding: 8px;'>{$sample->created_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No sample data found</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
