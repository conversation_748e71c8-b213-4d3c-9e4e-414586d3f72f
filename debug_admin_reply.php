<?php
/**
 * Debug Admin Reply Issue
 * 
 * This script checks why admin users cannot reply to messages
 */

// Start session
session_start();

// Include the configuration
require_once 'config/config.php';

// Load helpers first
require_once 'helpers/url_helper.php';
require_once 'helpers/session_helper.php';
require_once 'helpers/csrf_helper.php';

// Load core classes
require_once 'core/Database.php';
require_once 'models/NotificationCenterModel.php';

echo "<h1>Admin Reply Debug</h1>";

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-warning'>";
        echo "<h3>Not Logged In</h3>";
        echo "<p>Please <a href='" . BASE_URL . "/auth/login'>login</a> to debug admin reply.</p>";
        echo "</div>";
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $notificationCenterModel = new NotificationCenterModel();
    $db = new Database();
    
    echo "<p><strong>Debugging for User ID:</strong> {$userId}</p>";
    
    echo "<h2>1. User Role Check</h2>";
    
    // Check user's role in users table
    $db->query("SELECT id, name, email, role FROM users WHERE id = :user_id");
    $db->bind(':user_id', $userId);
    $user = $db->single();
    
    if ($user) {
        echo "<p>✅ User found in database</p>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>User Details:</h4>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> {$user->id}</li>";
        echo "<li><strong>Name:</strong> " . htmlspecialchars($user->name) . "</li>";
        echo "<li><strong>Email:</strong> " . htmlspecialchars($user->email) . "</li>";
        echo "<li><strong>Role:</strong> " . htmlspecialchars($user->role ?? 'NULL') . "</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($user->role === 'admin') {
            echo "<p>✅ User has admin role in users table</p>";
        } else {
            echo "<p>❌ User does NOT have admin role in users table</p>";
            echo "<p><strong>Current role:</strong> " . ($user->role ?? 'NULL') . "</p>";
        }
    } else {
        echo "<p>❌ User not found in database</p>";
    }
    
    echo "<h2>2. Privileged Role Method Test</h2>";
    
    // Test the userHasPrivilegedRole method
    $hasPrivilegedRole = $notificationCenterModel->userHasPrivilegedRole($userId);
    
    if ($hasPrivilegedRole) {
        echo "<p>✅ userHasPrivilegedRole() returns TRUE</p>";
    } else {
        echo "<p>❌ userHasPrivilegedRole() returns FALSE</p>";
    }
    
    echo "<h2>3. Show Roles Check</h2>";
    
    // Check show roles
    try {
        $db->query("SELECT * FROM show_roles WHERE user_id = :user_id AND status = 'active'");
        $db->bind(':user_id', $userId);
        $showRoles = $db->resultSet();
        
        if (!empty($showRoles)) {
            echo "<p>✅ Found " . count($showRoles) . " active show roles</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Show ID</th>";
            echo "<th style='padding: 8px;'>Role</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($showRoles as $role) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$role->show_id}</td>";
                echo "<td style='padding: 8px;'>{$role->role}</td>";
                echo "<td style='padding: 8px;'>{$role->status}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ No active show roles found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking show roles: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. Global Roles Check</h2>";
    
    // Check user_roles table
    try {
        $db->query("SELECT * FROM user_roles WHERE user_id = :user_id AND status = 'active'");
        $db->bind(':user_id', $userId);
        $userRoles = $db->resultSet();
        
        if (!empty($userRoles)) {
            echo "<p>✅ Found " . count($userRoles) . " active user roles</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Role</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($userRoles as $role) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$role->role}</td>";
                echo "<td style='padding: 8px;'>{$role->status}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ No active user roles found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error checking user roles: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Test Message Reply Check</h2>";
    
    // Get a test message
    $messageId = $_GET['message_id'] ?? null;
    
    if ($messageId) {
        echo "<h3>Testing Message ID: {$messageId}</h3>";
        
        // Check if user can reply to this message
        $canReply = $notificationCenterModel->canUserReplyToMessage($userId, $messageId);
        
        if ($canReply) {
            echo "<p>✅ canUserReplyToMessage() returns TRUE</p>";
        } else {
            echo "<p>❌ canUserReplyToMessage() returns FALSE</p>";
        }
        
        // Get message details
        $db->query("SELECT * FROM user_messages WHERE id = :message_id");
        $db->bind(':message_id', $messageId);
        $message = $db->single();
        
        if ($message) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Message Details:</h4>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> {$message->id}</li>";
            echo "<li><strong>From User:</strong> {$message->from_user_id}</li>";
            echo "<li><strong>To User:</strong> {$message->to_user_id}</li>";
            echo "<li><strong>Subject:</strong> " . htmlspecialchars($message->subject) . "</li>";
            echo "<li><strong>Allows Reply:</strong> " . ($message->allows_reply ?? 'NULL') . "</li>";
            echo "<li><strong>Reply Used:</strong> " . ($message->reply_used ?? 'NULL') . "</li>";
            echo "<li><strong>Show ID:</strong> " . ($message->show_id ?? 'NULL') . "</li>";
            echo "</ul>";
            echo "</div>";
            
            // Check if user is recipient
            if ($message->to_user_id == $userId) {
                echo "<p>✅ You are the recipient of this message</p>";
            } else {
                echo "<p>❌ You are NOT the recipient of this message</p>";
            }
        } else {
            echo "<p>❌ Message not found</p>";
        }
        
    } else {
        echo "<p>Add <code>?message_id=X</code> to test a specific message</p>";
        
        // Show recent messages
        $db->query("SELECT id, subject, to_user_id FROM user_messages WHERE to_user_id = :user_id ORDER BY created_at DESC LIMIT 5");
        $db->bind(':user_id', $userId);
        $recentMessages = $db->resultSet();
        
        if (!empty($recentMessages)) {
            echo "<p>Recent messages to test:</p>";
            echo "<ul>";
            foreach ($recentMessages as $msg) {
                $testUrl = $_SERVER['REQUEST_URI'] . "?message_id=" . $msg->id;
                echo "<li><a href='{$testUrl}'>Message {$msg->id}: " . htmlspecialchars(substr($msg->subject, 0, 50)) . "...</a></li>";
            }
            echo "</ul>";
        }
    }
    
    echo "<h2>6. Quick Fix</h2>";
    
    if ($_POST['action'] ?? '' === 'fix_admin_role') {
        try {
            $db->query("UPDATE users SET role = 'admin' WHERE id = :user_id");
            $db->bind(':user_id', $userId);
            
            if ($db->execute()) {
                echo "<div class='alert alert-success'>";
                echo "<h4>Admin Role Set!</h4>";
                echo "<p>Your role has been set to 'admin'. <a href=''>Refresh this page</a> to see the changes.</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h4>Failed to Set Admin Role</h4>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>Error Setting Admin Role</h4>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    if ($user && $user->role !== 'admin') {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Fix Admin Role</h4>";
        echo "<p>Your role is currently '<strong>" . ($user->role ?? 'NULL') . "</strong>' but should be '<strong>admin</strong>' to reply to all messages.</p>";
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='action' value='fix_admin_role'>";
        echo "<button type='submit' class='btn btn-primary'>Set My Role to Admin</button>";
        echo "</form>";
        echo "</div>";
    }
    
    echo "<h2>7. Navigation</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<p>Test the system:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/notification_center' target='_blank'>Message Center</a></li>";
    echo "<li><a href='" . BASE_URL . "/test_reply_system.php' target='_blank'>Reply System Test</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; text-decoration: none; display: inline-block; }
.btn:hover { background: #0056b3; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
